# 🧪 Stripe Connect Quick Test Guide

## Pre-Connection Test

Before connecting Stripe, verify the integration is properly set up:

### 1. Check Environment Configuration

Run this command to verify Stripe configuration:

```bash
php artisan tinker
```

Then in tinker:

```php
// Check if Stripe config is loaded
config('services.stripe');

// Should return something like:
// [
//   "key" => "pk_test_...",
//   "secret" => "sk_test_...",
//   "client_id" => "ca_...",
//   "webhook_secret" => "whsec_...",
//   "platform_fee_rate" => 0.025
// ]
```

### 2. Test Service Instantiation

```php
// Test if StripeConnectService can be instantiated
$stripeService = app(\App\Services\StripeConnectService::class);
echo "StripeConnectService loaded successfully!";
```

### 3. Check Database Tables

```php
// Verify tables exist
\Schema::hasTable('payment_gateway_accounts'); // Should return true
\Schema::hasTable('payment_transactions');     // Should return true
\Schema::hasTable('revenue_records');          // Should return true
```

Exit tinker with `exit`.

## Connection Test Steps

### Step 1: Access Gateway Settings

1. Login to your bookkei application
2. Navigate to `/owner/payments/gateways`
3. You should see the Stripe Connect card with "Not Connected" status

### Step 2: Test Environment Variables

Before clicking "Connect with Stripe", ensure your `.env` file has:

```env
# Test Mode Stripe Keys (get these from Stripe Dashboard)
STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef...
STRIPE_SECRET_KEY=sk_test_51234567890abcdef...
STRIPE_CLIENT_ID=ca_1234567890abcdef...
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
STRIPE_PLATFORM_FEE_RATE=0.025
```

### Step 3: Clear Configuration Cache

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
```

### Step 4: Test Connection Flow

1. Click **"Connect with Stripe"** button
2. You should be redirected to Stripe's onboarding page
3. Complete the test account setup:
   - Use test business information
   - Use test bank account: `************` (routing: `*********`)
   - Complete all required fields

### Step 5: Verify Connection

After completing Stripe onboarding:

1. You'll be redirected back to `/owner/payments/gateways`
2. Stripe card should show "Connected" status
3. Account details should be displayed
4. You should see account capabilities and status

## Test Payment Processing

### Step 6: Test Payment API

Create a test file `test_stripe_payment.php`:

```php
<?php
// Test Stripe payment processing
require_once 'vendor/autoload.php';

// Simulate a payment request
$paymentData = [
    'business_id' => 1, // Your business ID
    'amount' => 5000, // $50.00 in cents
    'currency' => 'usd',
    'payment_method_id' => 'pm_card_visa', // Test payment method
    'customer_id' => 1,
    'booking_id' => 1,
    'service_id' => 1
];

// Make API call to your payment endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/bookkei/owner/payments/stripe/process');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-CSRF-TOKEN: your-csrf-token-here'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

echo "Payment Response: " . $response;
```

### Step 7: Test Webhook Endpoint

Test if webhook endpoint is accessible:

```bash
curl -X POST http://localhost/bookkei/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

Should return: `Invalid signature` (this is expected - means endpoint is working)

## Troubleshooting Common Issues

### Issue 1: "Connect with Stripe" Button Not Working

**Symptoms**: Button click does nothing or shows error

**Solutions**:
1. Check browser console for JavaScript errors
2. Verify CSRF token is present: `<meta name="csrf-token" content="...">`
3. Check if routes are properly registered:
   ```bash
   php artisan route:list | grep stripe
   ```

### Issue 2: Stripe Keys Not Working

**Symptoms**: "Invalid API key" or similar errors

**Solutions**:
1. Verify keys are from the same Stripe account
2. Ensure you're using test keys (start with `pk_test_` and `sk_test_`)
3. Check if Connect is enabled in your Stripe account
4. Verify Client ID matches your Connect application

### Issue 3: Redirect Issues

**Symptoms**: Redirect loops or 404 errors after Stripe onboarding

**Solutions**:
1. Check return URL is accessible: `/owner/payments/stripe/return`
2. Verify refresh URL is accessible: `/owner/payments/stripe/refresh`
3. Ensure URLs are using correct domain (not localhost for webhooks)

### Issue 4: Database Errors

**Symptoms**: SQL errors when connecting account

**Solutions**:
1. Run migrations: `php artisan migrate`
2. Check database connection: `php artisan tinker` then `DB::connection()->getPdo()`
3. Verify table structure matches models

## Success Indicators

✅ **Connection Successful** when you see:
- Stripe card shows "Connected" status
- Account ID is displayed (starts with `acct_`)
- Capabilities show "active" status
- No error messages in logs

✅ **Payment Processing Ready** when:
- Test payment API returns success response
- Transaction records are created in database
- Revenue records are automatically generated
- Webhook endpoint responds correctly

## Next Steps After Successful Test

1. **Configure Production Keys** (when ready to go live)
2. **Set up Real Webhooks** in Stripe Dashboard
3. **Test with Real Cards** (small amounts)
4. **Monitor Logs** for any issues
5. **Set up Monitoring** for payment success rates

## Debug Commands

```bash
# Check logs for errors
tail -f storage/logs/laravel.log

# Test database connection
php artisan tinker
DB::connection()->getPdo();

# Check Stripe service
$stripe = app(\App\Services\StripeConnectService::class);

# Verify middleware is working
php artisan route:list --middleware=financial.isolation
```

## Support

If you encounter issues:

1. Check the application logs: `storage/logs/laravel.log`
2. Review Stripe Dashboard for account status
3. Verify webhook delivery in Stripe Dashboard
4. Test with different browsers/incognito mode
5. Check network connectivity and firewall settings

The integration is ready to test! 🚀
