<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Title
    |--------------------------------------------------------------------------
    |
    | Here you can change the default title of your owner panel.
    |
    | For detailed instructions you can look the title section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'title' => 'BookKei Owner',
    'title_prefix' => '',
    'title_postfix' => ' - Business Owner Portal',

    /*
    |--------------------------------------------------------------------------
    | Favicon
    |--------------------------------------------------------------------------
    |
    | Here you can activate the favicon.
    |
    | For detailed instructions you can look the favicon section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_ico_only' => false,
    'use_full_favicon' => false,

    /*
    |--------------------------------------------------------------------------
    | Google Fonts
    |--------------------------------------------------------------------------
    |
    | Here you can allow or not the use of external google fonts. Disabling the
    | google fonts may be useful if your admin panel internet access is
    | restricted somehow.
    |
    | For detailed instructions you can look the google fonts section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'google_fonts' => [
        'allowed' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Owner Panel Logo
    |--------------------------------------------------------------------------
    |
    | Here you can change the logo of your owner panel.
    |
    | For detailed instructions you can look the logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'logo' => '<b>Book</b>Kei <span class="text-sm">Owner</span>',
    'logo_img' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
    'logo_img_class' => 'brand-image img-circle elevation-3',
    'logo_img_xl' => null,
    'logo_img_xl_class' => 'brand-image-xs',
    'logo_img_alt' => 'BookKei Owner Logo',

    /*
    |--------------------------------------------------------------------------
    | Authentication Logo
    |--------------------------------------------------------------------------
    |
    | Here you can setup an alternative logo to use on your login and register
    | screens. When disabled, the admin panel logo will be used instead.
    |
    | For detailed instructions you can look the auth logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'auth_logo' => [
        'enabled' => false,
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'Owner Auth Logo',
            'class' => '',
            'width' => 50,
            'height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Preloader Animation
    |--------------------------------------------------------------------------
    |
    | Here you can change the preloader animation configuration. Currently, two
    | modes are supported: 'fullscreen' for a fullscreen preloader animation
    | and 'cwrapper' to attach the preloader animation into the content-wrapper
    | element and avoid overlapping it with the sidebars and the top navbar.
    |
    | For detailed instructions you can look the preloader section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'preloader' => [
        'enabled' => true,
        'mode' => 'fullscreen',
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'BookKei Owner Preloader',
            'effect' => 'animation__shake',
            'width' => 60,
            'height' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Menu
    |--------------------------------------------------------------------------
    |
    | Here you can activate and change the user menu.
    |
    | For detailed instructions you can look the user menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'usermenu_enabled' => true,
    'usermenu_header' => false,
    'usermenu_header_class' => 'bg-success',
    'usermenu_image' => false,
    'usermenu_desc' => false,
    'usermenu_profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Layout
    |--------------------------------------------------------------------------
    |
    | Here we change the layout of your owner panel.
    |
    | For detailed instructions you can look the layout section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'layout_topnav' => null,
    'layout_boxed' => null,
    'layout_fixed_sidebar' => null,
    'layout_fixed_navbar' => null,
    'layout_fixed_footer' => null,
    'layout_dark_mode' => null,

    /*
    |--------------------------------------------------------------------------
    | Authentication Views Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the authentication views.
    |
    | For detailed instructions you can look the auth classes section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_auth_card' => 'card-outline card-success',
    'classes_auth_header' => '',
    'classes_auth_body' => '',
    'classes_auth_footer' => '',
    'classes_auth_icon' => '',
    'classes_auth_btn' => 'btn-flat btn-success',

    /*
    |--------------------------------------------------------------------------
    | Owner Panel Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the owner panel.
    |
    | For detailed instructions you can look the admin panel classes here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_body' => '',
    'classes_brand' => '',
    'classes_brand_text' => '',
    'classes_content_wrapper' => '',
    'classes_content_header' => '',
    'classes_content' => '',
    'classes_sidebar' => 'sidebar-dark-success elevation-4',
    'classes_sidebar_nav' => '',
    'classes_topnav' => 'navbar-success navbar-dark',
    'classes_topnav_nav' => 'navbar-expand',
    'classes_topnav_container' => 'container',

    /*
    |--------------------------------------------------------------------------
    | Sidebar
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar of the owner panel.
    |
    | For detailed instructions you can look the sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'sidebar_mini' => 'lg',
    'sidebar_collapse' => false,
    'sidebar_collapse_auto_size' => false,
    'sidebar_collapse_remember' => false,
    'sidebar_collapse_remember_no_transition' => true,
    'sidebar_scrollbar_theme' => 'os-theme-light',
    'sidebar_scrollbar_auto_hide' => 'l',
    'sidebar_nav_accordion' => true,
    'sidebar_nav_animation_speed' => 300,

    /*
    |--------------------------------------------------------------------------
    | Control Sidebar (Right Sidebar)
    |--------------------------------------------------------------------------
    |
    | Here we can modify the right sidebar aka control sidebar of the owner panel.
    |
    | For detailed instructions you can look the right sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'right_sidebar' => false,
    'right_sidebar_icon' => 'fas fa-cogs',
    'right_sidebar_theme' => 'dark',
    'right_sidebar_slide' => true,
    'right_sidebar_push' => true,
    'right_sidebar_scrollbar_theme' => 'os-theme-light',
    'right_sidebar_scrollbar_auto_hide' => 'l',

    /*
    |--------------------------------------------------------------------------
    | URLs
    |--------------------------------------------------------------------------
    |
    | Here we can modify the url settings of the owner panel.
    |
    | For detailed instructions you can look the urls section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_route_url' => false,
    'dashboard_url' => 'owner/dashboard',
    'logout_url' => 'logout',
    'login_url' => 'login',
    'register_url' => 'register',
    'password_reset_url' => 'password/reset',
    'password_email_url' => 'password/email',
    'profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Laravel Mix
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Laravel Mix option for the owner panel.
    |
    | For detailed instructions you can look the laravel mix section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'enabled_laravel_mix' => false,
    'laravel_mix_css_path' => 'css/app.css',
    'laravel_mix_js_path' => 'js/app.js',

    /*
    |--------------------------------------------------------------------------
    | Menu Items
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar/top navigation of the owner panel.
    |
    | For detailed instructions you can look the menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'menu' => [
        // Navbar items:
        [
            'type' => 'navbar-search',
            'text' => 'search',
            'topnav_right' => true,
        ],
        [
            'type' => 'fullscreen-widget',
            'topnav_right' => true,
        ],

        // Sidebar items:
        [
            'type' => 'sidebar-menu-search',
            'text' => 'search',
        ],
        [
            'text' => 'Dashboard',
            'url' => 'owner/dashboard',
            'icon' => 'fas fa-fw fa-tachometer-alt',
            'active' => ['owner/dashboard*'],
        ],
        ['header' => 'MY BUSINESS'],
        [
            'text' => 'Business Overview',
            'url' => 'owner/business',
            'icon' => 'fas fa-fw fa-building',
            'active' => ['owner/business*'],
        ],
        [
            'text' => 'Business Settings',
            'icon' => 'fas fa-fw fa-cogs',
            'submenu' => [
                [
                    'text' => 'General Information',
                    'url' => 'owner/business/general',
                    'icon' => 'fas fa-fw fa-info-circle',
                ],
                [
                    'text' => 'Operating Hours',
                    'url' => 'owner/business/operating-hours',
                    'icon' => 'fas fa-fw fa-clock',
                ],
                [
                    'text' => 'Holidays & Closures',
                    'url' => 'owner/business/holidays',
                    'icon' => 'fas fa-fw fa-calendar-times',
                ],
                [
                    'text' => 'Locations',
                    'url' => 'owner/business/locations',
                    'icon' => 'fas fa-fw fa-map-marker-alt',
                ],
                [
                    'text' => 'Branding & Themes',
                    'url' => 'owner/business/branding',
                    'icon' => 'fas fa-fw fa-palette',
                ],
                [
                    'text' => 'Landing Page',
                    'url' => 'owner/landing-page',
                    'icon' => 'fas fa-fw fa-globe',
                ],
                [
                    'text' => 'Service Display',
                    'url' => 'owner/service-display',
                    'icon' => 'fas fa-fw fa-th-large',
                ],
            ],
        ],
        ['header' => 'SERVICES & RESOURCES'],
        [
            'text' => 'Services',
            'icon' => 'fas fa-fw fa-concierge-bell',
            'submenu' => [
                [
                    'text' => 'All Services',
                    'url' => 'owner/services',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'Add Service',
                    'url' => 'owner/services/create',
                    'icon' => 'fas fa-fw fa-plus',
                ],
                [
                    'text' => 'Service Categories',
                    'url' => 'owner/service-categories',
                    'icon' => 'fas fa-fw fa-tags',
                ],
            ],
        ],
        [
            'text' => 'Resources',
            'icon' => 'fas fa-fw fa-tools',
            'submenu' => [
                [
                    'text' => 'All Resources',
                    'url' => 'owner/resources',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'Add Resource',
                    'url' => 'owner/resources/create',
                    'icon' => 'fas fa-fw fa-plus',
                ],
                [
                    'text' => 'Resource Types',
                    'url' => 'owner/resource-types',
                    'icon' => 'fas fa-fw fa-tags',
                ],
            ],
        ],
        ['header' => 'BOOKINGS & CALENDAR'],
        [
            'text' => 'Today\'s Schedule',
            'url' => 'owner/schedule/today',
            'icon' => 'fas fa-fw fa-calendar-day',
            'label' => 'Now',
            'label_color' => 'info',
        ],
        [
            'text' => 'Calendar',
            'url' => 'owner/calendar',
            'icon' => 'fas fa-fw fa-calendar-alt',
            'active' => ['owner/calendar*'],
        ],
        [
            'text' => 'Bookings',
            'icon' => 'fas fa-fw fa-calendar-check',
            'submenu' => [
                [
                    'text' => 'All Bookings',
                    'url' => 'owner/bookings',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'New Booking',
                    'url' => 'owner/bookings/create',
                    'icon' => 'fas fa-fw fa-plus',
                ],
                [
                    'text' => 'Check-In Dashboard',
                    'url' => 'owner/check-in',
                    'icon' => 'fas fa-fw fa-sign-in-alt',
                ],
            ],
        ],
        [
            'text' => 'Waiting Lists',
            'url' => 'owner/waiting-lists',
            'icon' => 'fas fa-fw fa-clock',
            'active' => ['owner/waiting-lists*'],
        ],
        ['header' => 'CUSTOMERS & COMMUNICATION'],
        [
            'text' => 'Customers',
            'icon' => 'fas fa-fw fa-users',
            'submenu' => [
                [
                    'text' => 'All Customers',
                    'url' => 'owner/customers',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'Add Customer',
                    'url' => 'owner/customers/create',
                    'icon' => 'fas fa-fw fa-plus',
                ],
                [
                    'text' => 'Customer Tags',
                    'url' => 'owner/customer-tags',
                    'icon' => 'fas fa-fw fa-tags',
                ],
            ],
        ],
        [
            'text' => 'Communications',
            'icon' => 'fas fa-fw fa-comments',
            'submenu' => [
                [
                    'text' => 'Notifications',
                    'url' => 'owner/notifications',
                    'icon' => 'fas fa-fw fa-bell',
                ],
                [
                    'text' => 'Notification Preferences',
                    'url' => 'owner/notification-preferences',
                    'icon' => 'fas fa-fw fa-cog',
                ],
            ],
        ],
        ['header' => 'REPORTS & ANALYTICS'],
        [
            'text' => 'Business Analytics',
            'url' => 'owner/analytics',
            'icon' => 'fas fa-fw fa-chart-line',
            'active' => ['owner/analytics*'],
        ],
        [
            'text' => 'Reports',
            'icon' => 'fas fa-fw fa-chart-bar',
            'submenu' => [
                [
                    'text' => 'Booking Reports',
                    'url' => 'owner/reports/bookings',
                    'icon' => 'fas fa-fw fa-calendar-check',
                ],
                [
                    'text' => 'Revenue Reports',
                    'url' => 'owner/reports/revenue',
                    'icon' => 'fas fa-fw fa-dollar-sign',
                ],
                [
                    'text' => 'Customer Reports',
                    'url' => 'owner/reports/customers',
                    'icon' => 'fas fa-fw fa-users',
                ],
                [
                    'text' => 'Resource Utilization',
                    'url' => 'owner/reports/resources',
                    'icon' => 'fas fa-fw fa-tools',
                ],
            ],
        ],
        ['header' => 'FINANCIAL MANAGEMENT'],
        [
            'text' => 'Revenue Overview',
            'url' => 'owner/revenue',
            'icon' => 'fas fa-fw fa-money-bill-wave',
            'active' => ['owner/revenue*'],
        ],
        [
            'text' => 'Payment Settings',
            'url' => 'owner/payments',
            'icon' => 'fas fa-fw fa-credit-card',
            'active' => ['owner/payments*'],
        ],
        [
            'text' => 'Pricing & Packages',
            'url' => 'owner/pricing',
            'icon' => 'fas fa-fw fa-tags',
            'active' => ['owner/pricing*'],
        ],
        ['header' => 'ACCOUNT'],
        [
            'text' => 'Profile',
            'url' => 'owner/profile',
            'icon' => 'fas fa-fw fa-user',
            'active' => ['owner/profile*'],
        ],
        [
            'text' => 'Security',
            'url' => 'owner/security',
            'icon' => 'fas fa-fw fa-shield-alt',
            'active' => ['owner/security*'],
        ],
        [
            'text' => 'Subscription',
            'url' => 'owner/subscription',
            'icon' => 'fas fa-fw fa-crown',
            'active' => ['owner/subscription*'],
            'label' => 'Pro',
            'label_color' => 'warning',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Menu Filters
    |--------------------------------------------------------------------------
    |
    | Here we can modify the menu filters of the owner panel.
    |
    | For detailed instructions you can look the menu filters section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'filters' => [
        JeroenNoten\LaravelAdminLte\Menu\Filters\GateFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\HrefFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\SearchFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ActiveFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ClassesFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\LangFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\DataFilter::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugins Initialization
    |--------------------------------------------------------------------------
    |
    | Here we can modify the plugins used inside the owner panel.
    |
    | For detailed instructions you can look the plugins section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Plugins-Configuration
    |
    */

    'plugins' => [
        'Datatables' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/dataTables.bootstrap4.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/css/dataTables.bootstrap4.min.css',
                ],
            ],
        ],
        'Select2' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.css',
                ],
            ],
        ],
        'Chartjs' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
                ],
            ],
        ],
        'Sweetalert2' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/sweetalert2@11',
                ],
            ],
        ],
        'Toastr' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css',
                ],
            ],
        ],
        'Pace' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/themes/blue/pace-theme-center-radar.min.css',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js',
                ],
            ],
        ],
        'FullCalendar' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/fullcalendar@6.1.8/main.min.css',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | IFrame
    |--------------------------------------------------------------------------
    |
    | Here we change the IFrame mode configuration. Note these changes will
    | only apply to the view that extends and enable the IFrame mode.
    |
    | For detailed instructions you can look the iframe mode section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/IFrame-Mode-Configuration
    |
    */

    'iframe' => [
        'default_tab' => [
            'url' => null,
            'title' => null,
        ],
        'buttons' => [
            'close' => true,
            'close_all' => true,
            'close_all_other' => true,
            'scroll_left' => true,
            'scroll_right' => true,
            'fullscreen' => true,
        ],
        'options' => [
            'loading_screen' => 1000,
            'auto_show_new_tab' => true,
            'use_navbar_items' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Livewire
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Livewire support.
    |
    | For detailed instructions you can look the livewire here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'livewire' => false,
];
