<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Business;
use App\Models\PaymentTransaction;
use App\Models\RevenueRecord;
use App\Models\PricingStrategy;
use App\Models\PricingExperiment;
use App\Models\FinancialMetric;
use App\Models\PaymentGatewayAccount;
use App\Models\BusinessFinancialConfig;
use App\Models\FinancialAlert;
use Illuminate\Database\Eloquent\Builder;

class FinancialIsolationMiddleware
{
    /**
     * Handle an incoming request.
     * Applies enterprise isolation across all financial modules ensuring
     * complete data separation between business owners.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply isolation for authenticated users
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Get the current business context
        $businessId = $this->getCurrentBusinessId($request, $user);

        if (!$businessId) {
            // If no business context, allow request to proceed (will handle in controllers)
            return $next($request);
        }

        // Verify user has access to this business
        if (!$this->verifyBusinessAccess($user, $businessId)) {
            abort(403, 'Access denied to this business data.');
        }

        // Apply global scopes for complete financial isolation
        $this->applyFinancialIsolationScopes($businessId, $user->id);

        return $next($request);
    }

    /**
     * Get the current business ID from various sources
     */
    private function getCurrentBusinessId(Request $request, $user): ?int
    {
        // Try to get business ID from various sources in order of preference

        // 1. From request parameter (for API calls or specific routes)
        if ($request->route('business')) {
            return $request->route('business');
        }

        // 2. From request input
        if ($request->has('business_id')) {
            return $request->input('business_id');
        }

        // 3. From session (for web interface)
        if (session('current_business_id')) {
            return session('current_business_id');
        }

        // 4. From user's primary business (fallback)
        if (method_exists($user, 'currentBusiness') && $user->currentBusiness) {
            return $user->currentBusiness->id;
        }

        // 5. From user's first owned business
        $business = Business::where('owner_id', $user->id)->first();
        if ($business) {
            // Set as current business in session for consistency
            session(['current_business_id' => $business->id]);
            return $business->id;
        }

        return null;
    }

    /**
     * Verify that the user has access to the specified business
     */
    private function verifyBusinessAccess($user, int $businessId): bool
    {
        // Admin users have access to all businesses
        if ($user->hasRole('admin')) {
            return true;
        }

        // Business owners have access to their own businesses
        $business = Business::find($businessId);
        if ($business && $business->owner_id === $user->id) {
            return true;
        }

        // Staff members might have access based on permissions
        // This would need to be implemented based on your staff permission system

        return false;
    }

    /**
     * Apply global scopes for complete financial data isolation
     */
    private function applyFinancialIsolationScopes(int $businessId, int $userId): void
    {
        // Payment Transactions Isolation
        PaymentTransaction::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Revenue Records Isolation
        RevenueRecord::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Pricing Strategies Isolation
        PricingStrategy::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Pricing Experiments Isolation
        PricingExperiment::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Financial Metrics Isolation
        FinancialMetric::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Payment Gateway Accounts Isolation
        PaymentGatewayAccount::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Business Financial Config Isolation
        BusinessFinancialConfig::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });

        // Financial Alerts Isolation
        FinancialAlert::addGlobalScope('financial_isolation', function (Builder $builder) use ($businessId) {
            $builder->where('business_id', $businessId);
        });
    }

    /**
     * Log security access attempt for audit purposes
     */
    private function logSecurityAccess($user, $businessId, $action = 'access'): void
    {
        // This could be enhanced to log to a security audit table
        Log::info('Financial data access', [
            'user_id' => $user->id,
            'business_id' => $businessId,
            'action' => $action,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * Additional security validation for sensitive financial operations
     */
    private function validateSensitiveOperation(Request $request, $user): bool
    {
        // Check for rate limiting on financial operations
        $key = 'financial_operations:' . $user->id;
        $attempts = cache()->get($key, 0);

        if ($attempts > 100) { // Max 100 financial operations per hour
            return false;
        }

        cache()->put($key, $attempts + 1, now()->addHour());

        return true;
    }

    /**
     * Validate business ownership for financial operations
     */
    public static function validateBusinessOwnership(int $businessId, int $userId): bool
    {
        $business = Business::find($businessId);
        return $business && $business->owner_id === $userId;
    }

    /**
     * Get allowed business IDs for a user
     */
    public static function getAllowedBusinessIds($user): array
    {
        if ($user->hasRole('admin')) {
            return Business::pluck('id')->toArray();
        }

        return Business::where('owner_id', $user->id)->pluck('id')->toArray();
    }
}
