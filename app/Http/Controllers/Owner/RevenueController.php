<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Business;
use App\Models\RevenueRecord;
use App\Models\PaymentTransaction;
use App\Models\Service;
use App\Models\User;
use App\Models\FinancialMetric;
use Carbon\Carbon;

class RevenueController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'financial.isolation']);
    }

    /**
     * Display the revenue analytics dashboard
     */
    public function index(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);

        // Get revenue overview metrics
        $revenueMetrics = $this->getRevenueMetrics($businessId, $dateRange);

        // Get revenue by time periods
        $dailyRevenue = $this->getDailyRevenue($businessId, 30);
        $monthlyRevenue = $this->getMonthlyRevenue($businessId);

        // Get revenue by service
        $serviceRevenue = $this->getRevenueByService($businessId, $dateRange);

        // Get top customers by revenue
        $topCustomers = $this->getTopCustomers($businessId, $dateRange);

        // Get payment method breakdown
        $paymentMethodBreakdown = $this->getPaymentMethodBreakdown($businessId, $dateRange);

        // Get revenue growth data
        $revenueGrowth = $this->getRevenueGrowth($businessId);

        return view('owner.revenue.index', compact(
            'revenueMetrics',
            'dailyRevenue',
            'monthlyRevenue',
            'serviceRevenue',
            'topCustomers',
            'paymentMethodBreakdown',
            'revenueGrowth',
            'dateRange'
        ));
    }

    /**
     * Show detailed revenue analytics
     */
    public function analytics(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);

        // Advanced analytics data
        $cohortAnalysis = $this->getCohortAnalysis($businessId);
        $customerLifetimeValue = $this->getCustomerLifetimeValue($businessId);
        $revenueForecasting = $this->getRevenueForecasting($businessId);
        $seasonalTrends = $this->getSeasonalTrends($businessId);

        return view('owner.revenue.analytics', compact(
            'cohortAnalysis',
            'customerLifetimeValue',
            'revenueForecasting',
            'seasonalTrends',
            'dateRange'
        ));
    }

    /**
     * Show service performance
     */
    public function servicePerformance(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);

        // Get all services with revenue data
        $services = Service::where('business_id', $businessId)
                          ->with(['revenueRecords' => function($query) use ($dateRange) {
                              $query->when($dateRange, function($q) use ($dateRange) {
                                  $q->whereBetween('revenue_date', $dateRange);
                              });
                          }])
                          ->get()
                          ->map(function($service) {
                              $revenueRecords = $service->revenueRecords;

                              return [
                                  'service' => $service,
                                  'total_revenue' => $revenueRecords->sum('net_amount'),
                                  'total_transactions' => $revenueRecords->count(),
                                  'average_transaction' => $revenueRecords->avg('net_amount') ?: 0,
                                  'profit_margin' => $this->calculateServiceProfitMargin($service, $revenueRecords),
                              ];
                          })
                          ->sortByDesc('total_revenue');

        return view('owner.revenue.service-performance', compact('services', 'dateRange'));
    }

    /**
     * Show customer insights
     */
    public function customerInsights(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);

        // Customer revenue analysis
        $customerMetrics = $this->getCustomerRevenueMetrics($businessId, $dateRange);
        $customerSegmentation = $this->getCustomerSegmentation($businessId, $dateRange);
        $customerRetention = $this->getCustomerRetention($businessId);

        return view('owner.revenue.customer-insights', compact(
            'customerMetrics',
            'customerSegmentation',
            'customerRetention',
            'dateRange'
        ));
    }

    /**
     * Get revenue data for API/AJAX calls
     */
    public function getRevenueData(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $type = $request->get('type', 'daily');
        $dateRange = $this->getDateRange($request);

        switch ($type) {
            case 'daily':
                $data = $this->getDailyRevenue($businessId, 30);
                break;
            case 'weekly':
                $data = $this->getWeeklyRevenue($businessId);
                break;
            case 'monthly':
                $data = $this->getMonthlyRevenue($businessId);
                break;
            case 'service':
                $data = $this->getRevenueByService($businessId, $dateRange);
                break;
            case 'customer':
                $data = $this->getTopCustomers($businessId, $dateRange);
                break;
            default:
                $data = [];
        }

        return response()->json($data);
    }

    /**
     * Export revenue data
     */
    public function export(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $format = $request->get('format', 'csv');
        $dateRange = $this->getDateRange($request);

        $revenueData = RevenueRecord::where('business_id', $businessId)
                                   ->with(['service', 'customer', 'booking'])
                                   ->when($dateRange, function($query) use ($dateRange) {
                                       $query->whereBetween('revenue_date', $dateRange);
                                   })
                                   ->orderBy('revenue_date', 'desc')
                                   ->get();

        if ($format === 'csv') {
            return $this->exportCsv($revenueData);
        } elseif ($format === 'excel') {
            return $this->exportExcel($revenueData);
        }

        return back()->with('error', 'Invalid export format');
    }

    /**
     * Get current business ID
     */
    private function getCurrentBusinessId()
    {
        return session('current_business_id') ??
               Business::where('owner_id', Auth::id())->first()->id ??
               abort(404, 'No business found');
    }

    /**
     * Get date range from request
     */
    private function getDateRange(Request $request)
    {
        $period = $request->get('period', 'month');

        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'yesterday':
                return [now()->yesterday()->startOfDay(), now()->yesterday()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'quarter':
                return [now()->startOfQuarter(), now()->endOfQuarter()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            case 'custom':
                if ($request->has(['start_date', 'end_date'])) {
                    return [
                        Carbon::parse($request->start_date)->startOfDay(),
                        Carbon::parse($request->end_date)->endOfDay()
                    ];
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * Get revenue overview metrics
     */
    private function getRevenueMetrics($businessId, $dateRange = null)
    {
        return RevenueRecord::getRevenueMetrics($businessId, $dateRange);
    }

    /**
     * Get daily revenue for the last N days
     */
    private function getDailyRevenue($businessId, $days = 30)
    {
        return RevenueRecord::getDailyRevenue($businessId, $days);
    }

    /**
     * Get monthly revenue for the current year
     */
    private function getMonthlyRevenue($businessId)
    {
        return RevenueRecord::getRevenueByMonth($businessId);
    }

    /**
     * Get revenue by service
     */
    private function getRevenueByService($businessId, $dateRange = null)
    {
        return RevenueRecord::getRevenueByService($businessId, $dateRange);
    }

    /**
     * Get top customers by revenue
     */
    private function getTopCustomers($businessId, $dateRange = null)
    {
        return RevenueRecord::getRevenueByCustomer($businessId, $dateRange, 20);
    }

    /**
     * Get payment method breakdown
     */
    private function getPaymentMethodBreakdown($businessId, $dateRange = null)
    {
        $query = RevenueRecord::where('business_id', $businessId)
                             ->where('status', 'completed');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        return $query->selectRaw('payment_method, SUM(net_amount) as revenue, COUNT(*) as transactions')
                    ->groupBy('payment_method')
                    ->orderByDesc('revenue')
                    ->get();
    }

    /**
     * Get revenue growth data
     */
    private function getRevenueGrowth($businessId)
    {
        return RevenueRecord::getRevenueGrowth($businessId, 12);
    }

    /**
     * Get weekly revenue
     */
    private function getWeeklyRevenue($businessId)
    {
        return RevenueRecord::where('business_id', $businessId)
                           ->where('status', 'completed')
                           ->where('revenue_date', '>=', now()->subWeeks(12))
                           ->selectRaw('WEEK(revenue_date) as week, YEAR(revenue_date) as year, SUM(net_amount) as revenue')
                           ->groupBy('year', 'week')
                           ->orderBy('year')
                           ->orderBy('week')
                           ->get();
    }

    /**
     * Calculate service profit margin
     */
    private function calculateServiceProfitMargin($service, $revenueRecords)
    {
        $totalRevenue = $revenueRecords->sum('net_amount');
        $totalCosts = $revenueRecords->sum('gateway_fees') + $revenueRecords->sum('platform_fees');

        if ($totalRevenue > 0) {
            return (($totalRevenue - $totalCosts) / $totalRevenue) * 100;
        }

        return 0;
    }

    /**
     * Get customer revenue metrics
     */
    private function getCustomerRevenueMetrics($businessId, $dateRange = null)
    {
        $query = RevenueRecord::where('business_id', $businessId)
                             ->where('status', 'completed');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        $uniqueCustomers = $query->distinct('customer_id')->count('customer_id');
        $totalRevenue = $query->sum('net_amount');
        $averageRevenuePerCustomer = $uniqueCustomers > 0 ? $totalRevenue / $uniqueCustomers : 0;

        return [
            'unique_customers' => $uniqueCustomers,
            'total_revenue' => $totalRevenue,
            'average_revenue_per_customer' => $averageRevenuePerCustomer,
        ];
    }

    /**
     * Get customer segmentation
     */
    private function getCustomerSegmentation($businessId, $dateRange = null)
    {
        $query = RevenueRecord::where('business_id', $businessId)
                             ->where('status', 'completed');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        $customerRevenue = $query->selectRaw('customer_id, SUM(net_amount) as total_spent')
                                ->groupBy('customer_id')
                                ->get();

        // Segment customers
        $segments = [
            'high_value' => $customerRevenue->where('total_spent', '>=', 1000)->count(),
            'medium_value' => $customerRevenue->where('total_spent', '>=', 500)->where('total_spent', '<', 1000)->count(),
            'low_value' => $customerRevenue->where('total_spent', '<', 500)->count(),
        ];

        return $segments;
    }

    /**
     * Get customer retention data
     */
    private function getCustomerRetention($businessId)
    {
        // Simplified customer retention calculation
        $currentMonth = RevenueRecord::where('business_id', $businessId)
                                    ->whereMonth('revenue_date', now()->month)
                                    ->whereYear('revenue_date', now()->year)
                                    ->distinct('customer_id')
                                    ->count('customer_id');

        $previousMonth = RevenueRecord::where('business_id', $businessId)
                                     ->whereMonth('revenue_date', now()->subMonth()->month)
                                     ->whereYear('revenue_date', now()->subMonth()->year)
                                     ->distinct('customer_id')
                                     ->count('customer_id');

        $retentionRate = $previousMonth > 0 ? ($currentMonth / $previousMonth) * 100 : 0;

        return [
            'current_month_customers' => $currentMonth,
            'previous_month_customers' => $previousMonth,
            'retention_rate' => round($retentionRate, 2),
        ];
    }

    /**
     * Placeholder methods for advanced analytics
     */
    private function getCohortAnalysis($businessId)
    {
        // Implementation would involve complex cohort analysis
        return [];
    }

    private function getCustomerLifetimeValue($businessId)
    {
        // Implementation would calculate CLV based on historical data
        return [];
    }

    private function getRevenueForecasting($businessId)
    {
        // Implementation would use machine learning for forecasting
        return [];
    }

    private function getSeasonalTrends($businessId)
    {
        // Implementation would analyze seasonal patterns
        return [];
    }

    /**
     * Export data as CSV
     */
    private function exportCsv($revenueData)
    {
        $filename = 'revenue_report_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($revenueData) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date', 'Service', 'Customer', 'Gross Amount', 'Fees', 'Net Amount',
                'Payment Method', 'Status'
            ]);

            // CSV data
            foreach ($revenueData as $record) {
                fputcsv($file, [
                    $record->revenue_date,
                    $record->service->name ?? 'N/A',
                    $record->customer->name ?? 'N/A',
                    $record->gross_amount,
                    $record->gateway_fees + $record->platform_fees,
                    $record->net_amount,
                    $record->payment_method,
                    $record->status,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export data as Excel (placeholder)
     */
    private function exportExcel($revenueData)
    {
        // Implementation would use a package like PhpSpreadsheet
        return back()->with('info', 'Excel export functionality will be implemented.');
    }
}
