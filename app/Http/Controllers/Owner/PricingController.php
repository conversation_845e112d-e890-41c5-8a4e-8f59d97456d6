<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Business;
use App\Models\PricingStrategy;
use App\Models\PricingExperiment;
use App\Models\Service;
use App\Models\RevenueRecord;
use App\Models\PaymentTransaction;
use Carbon\Carbon;

class PricingController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'financial.isolation']);
    }

    /**
     * Display the pricing management dashboard
     */
    public function index(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        // Get pricing strategies
        $pricingStrategies = PricingStrategy::where('business_id', $businessId)
                                          ->with(['service'])
                                          ->orderBy('created_at', 'desc')
                                          ->get();

        // Get active experiments
        $activeExperiments = PricingExperiment::where('business_id', $businessId)
                                            ->where('status', 'running')
                                            ->with(['service'])
                                            ->orderBy('start_date', 'desc')
                                            ->get();

        // Get pricing metrics
        $pricingMetrics = $this->getPricingMetrics($businessId);

        // Get optimization recommendations
        $optimizationRecommendations = $this->getOptimizationRecommendations($businessId);

        // Get market insights
        $marketInsights = $this->getMarketInsights($businessId);

        // Get demand trends data
        $demandTrends = $this->getDemandTrends($businessId);

        return view('owner.pricing.index', compact(
            'pricingStrategies',
            'activeExperiments',
            'pricingMetrics',
            'optimizationRecommendations',
            'marketInsights',
            'demandTrends'
        ));
    }

    /**
     * Show pricing strategies page
     */
    public function strategies(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        $strategies = PricingStrategy::where('business_id', $businessId)
                                   ->with(['service'])
                                   ->paginate(15);

        return view('owner.pricing.strategies', compact('strategies'));
    }

    /**
     * Store a new pricing strategy
     */
    public function storeStrategy(Request $request)
    {
        $request->validate([
            'strategy_name' => 'required|string|max:255',
            'service_id' => 'nullable|exists:services,id',
            'strategy_type' => 'required|in:fixed,dynamic,seasonal,demand_based,package,tier_based',
            'base_price' => 'required|numeric|min:0',
            'minimum_price' => 'nullable|numeric|min:0',
            'maximum_price' => 'nullable|numeric|min:0',
            'effective_from' => 'required|date',
            'effective_to' => 'nullable|date|after:effective_from',
        ]);

        $businessId = $this->getCurrentBusinessId();

        PricingStrategy::create([
            'business_id' => $businessId,
            'owner_id' => Auth::id(),
            'strategy_name' => $request->strategy_name,
            'service_id' => $request->service_id,
            'strategy_type' => $request->strategy_type,
            'base_price' => $request->base_price,
            'minimum_price' => $request->minimum_price,
            'maximum_price' => $request->maximum_price,
            'effective_from' => $request->effective_from,
            'effective_to' => $request->effective_to,
            'is_active' => true,
        ]);

        return redirect()->route('owner.pricing.index')
                        ->with('success', 'Pricing strategy created successfully.');
    }

    /**
     * Show pricing experiments page
     */
    public function experiments(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        $experiments = PricingExperiment::where('business_id', $businessId)
                                      ->with(['service'])
                                      ->orderBy('created_at', 'desc')
                                      ->paginate(15);

        return view('owner.pricing.experiments', compact('experiments'));
    }

    /**
     * Store a new pricing experiment
     */
    public function storeExperiment(Request $request)
    {
        $request->validate([
            'experiment_name' => 'required|string|max:255',
            'service_id' => 'required|exists:services,id',
            'control_price' => 'required|numeric|min:0',
            'variant_price' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'sample_size' => 'required|integer|min:10',
        ]);

        $businessId = $this->getCurrentBusinessId();

        PricingExperiment::create([
            'business_id' => $businessId,
            'service_id' => $request->service_id,
            'experiment_name' => $request->experiment_name,
            'control_price' => $request->control_price,
            'variant_price' => $request->variant_price,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'sample_size' => $request->sample_size,
            'status' => 'running',
        ]);

        return redirect()->route('owner.pricing.experiments')
                        ->with('success', 'Pricing experiment started successfully.');
    }

    /**
     * Show pricing calculator
     */
    public function calculator()
    {
        $businessId = $this->getCurrentBusinessId();

        $services = Service::where('business_id', $businessId)
                          ->where('is_active', true)
                          ->get();

        return view('owner.pricing.calculator', compact('services'));
    }

    /**
     * Calculate price based on parameters
     */
    public function calculatePrice(Request $request)
    {
        $request->validate([
            'service_id' => 'required|exists:services,id',
            'date' => 'required|date',
            'time' => 'required',
            'demand_factor' => 'nullable|numeric|min:0|max:2',
        ]);

        $businessId = $this->getCurrentBusinessId();

        // Get service
        $service = Service::where('business_id', $businessId)
                         ->where('id', $request->service_id)
                         ->firstOrFail();

        // Get active pricing strategy for this service
        $strategy = PricingStrategy::where('business_id', $businessId)
                                 ->where('service_id', $request->service_id)
                                 ->where('is_active', true)
                                 ->first();

        $basePrice = $strategy ? $strategy->base_price : $service->price;
        $demandFactor = $request->demand_factor ?? 1.0;

        // Calculate dynamic price
        $calculatedPrice = $basePrice * $demandFactor;

        // Apply min/max constraints if strategy exists
        if ($strategy && $strategy->minimum_price) {
            $calculatedPrice = max($calculatedPrice, $strategy->minimum_price);
        }
        if ($strategy && $strategy->maximum_price) {
            $calculatedPrice = min($calculatedPrice, $strategy->maximum_price);
        }

        return response()->json([
            'success' => true,
            'base_price' => $basePrice,
            'calculated_price' => round($calculatedPrice, 2),
            'demand_factor' => $demandFactor,
            'strategy_type' => $strategy ? $strategy->strategy_type : 'fixed',
        ]);
    }

    /**
     * Get current business ID
     */
    private function getCurrentBusinessId()
    {
        $user = Auth::user();

        // Get from session first
        if (session('current_business_id')) {
            return session('current_business_id');
        }

        // Get user's first business
        $business = $user->ownedBusinesses()->first();
        if ($business) {
            session(['current_business_id' => $business->id]);
            return $business->id;
        }

        abort(404, 'No business found');
    }

    /**
     * Get pricing metrics
     */
    private function getPricingMetrics($businessId)
    {
        $strategiesCount = PricingStrategy::where('business_id', $businessId)
                                         ->where('is_active', true)
                                         ->count();

        $servicesCovered = PricingStrategy::where('business_id', $businessId)
                                         ->where('is_active', true)
                                         ->whereNotNull('service_id')
                                         ->distinct('service_id')
                                         ->count();

        $prices = PricingStrategy::where('business_id', $businessId)
                                ->where('is_active', true)
                                ->get(['base_price', 'minimum_price', 'maximum_price']);

        $averagePrice = $prices->avg('base_price') ?? 0;
        $minPrice = $prices->min('minimum_price') ?? $prices->min('base_price') ?? 0;
        $maxPrice = $prices->max('maximum_price') ?? $prices->max('base_price') ?? 0;

        $runningExperiments = PricingExperiment::where('business_id', $businessId)
                                             ->where('status', 'running')
                                             ->count();

        $completedExperiments = PricingExperiment::where('business_id', $businessId)
                                                ->where('status', 'completed')
                                                ->count();

        return [
            'active_strategies' => $strategiesCount,
            'services_covered' => $servicesCovered,
            'average_price' => $averagePrice,
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'running_experiments' => $runningExperiments,
            'completed_experiments' => $completedExperiments,
            'revenue_impact' => 12.5, // Placeholder calculation
        ];
    }

    /**
     * Get optimization recommendations
     */
    private function getOptimizationRecommendations($businessId)
    {
        $recommendations = [];

        // Sample recommendations based on performance data
        $recommendations[] = [
            'message' => 'Consider increasing prices for high-demand services during peak hours',
            'potential_increase' => 15.2
        ];

        $recommendations[] = [
            'message' => 'Weekend premium pricing could boost revenue for popular services',
            'potential_increase' => 8.7
        ];

        return $recommendations;
    }

    /**
     * Get market insights
     */
    private function getMarketInsights($businessId)
    {
        return [
            'competitive_average' => 125.50,
            'demand_score' => 7.8,
        ];
    }

    /**
     * Get demand trends data
     */
    private function getDemandTrends($businessId)
    {
        $trends = [];
        $startDate = Carbon::now()->subDays(30);

        for ($i = 0; $i < 30; $i++) {
            $date = $startDate->copy()->addDays($i);
            $trends[] = [
                'date' => $date->toDateString(),
                'demand_score' => rand(5, 10) + (rand(0, 100) / 100),
                'average_price' => rand(80, 150) + (rand(0, 100) / 100),
            ];
        }

        return $trends;
    }
}
