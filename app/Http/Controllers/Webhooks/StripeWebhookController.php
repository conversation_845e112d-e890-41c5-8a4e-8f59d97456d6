<?php

namespace App\Http\Controllers\Webhooks;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use App\Models\PaymentTransaction;
use App\Models\PaymentGatewayAccount;
use App\Models\RevenueRecord;
use App\Services\OwnerNotificationService;
use Carbon\Carbon;

class StripeWebhookController extends Controller
{
    protected $notificationService;

    public function __construct(OwnerNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Stripe webhook: Invalid payload', ['error' => $e->getMessage()]);
            return response('Invalid payload', 400);
        } catch (SignatureVerificationException $e) {
            Log::error('Stripe webhook: Invalid signature', ['error' => $e->getMessage()]);
            return response('Invalid signature', 400);
        }

        Log::info('Stripe webhook received', [
            'event_type' => $event->type,
            'event_id' => $event->id
        ]);

        // Handle the event
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event->data->object);
                break;

            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event->data->object);
                break;

            case 'payment_intent.requires_action':
                $this->handlePaymentIntentRequiresAction($event->data->object);
                break;

            case 'charge.dispute.created':
                $this->handleChargeDisputeCreated($event->data->object);
                break;

            case 'account.updated':
                $this->handleAccountUpdated($event->data->object);
                break;

            case 'payout.paid':
                $this->handlePayoutPaid($event->data->object);
                break;

            case 'payout.failed':
                $this->handlePayoutFailed($event->data->object);
                break;

            default:
                Log::info('Stripe webhook: Unhandled event type', ['type' => $event->type]);
        }

        return response('Webhook handled', 200);
    }

    /**
     * Handle successful payment intent
     */
    private function handlePaymentIntentSucceeded($paymentIntent)
    {
        $transaction = PaymentTransaction::where('gateway_payment_intent_id', $paymentIntent->id)->first();

        if (!$transaction) {
            Log::warning('Stripe webhook: Payment intent not found in database', [
                'payment_intent_id' => $paymentIntent->id
            ]);
            return;
        }

        // Update transaction status
        $transaction->update([
            'status' => 'completed',
            'processed_at' => now(),
            'gateway_response' => $paymentIntent->toArray(),
        ]);

        // Create or update revenue record
        $this->createOrUpdateRevenueRecord($transaction);

        // Send notification
        $this->sendPaymentNotification($transaction, 'payment_received');

        Log::info('Payment intent succeeded processed', [
            'transaction_id' => $transaction->id,
            'payment_intent_id' => $paymentIntent->id
        ]);
    }

    /**
     * Handle failed payment intent
     */
    private function handlePaymentIntentFailed($paymentIntent)
    {
        $transaction = PaymentTransaction::where('gateway_payment_intent_id', $paymentIntent->id)->first();

        if (!$transaction) {
            Log::warning('Stripe webhook: Payment intent not found in database', [
                'payment_intent_id' => $paymentIntent->id
            ]);
            return;
        }

        // Update transaction status
        $transaction->update([
            'status' => 'failed',
            'failure_reason' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
            'gateway_response' => $paymentIntent->toArray(),
        ]);

        // Send notification
        $this->sendPaymentNotification($transaction, 'payment_failed');

        Log::info('Payment intent failed processed', [
            'transaction_id' => $transaction->id,
            'payment_intent_id' => $paymentIntent->id,
            'failure_reason' => $transaction->failure_reason
        ]);
    }

    /**
     * Handle payment intent that requires action
     */
    private function handlePaymentIntentRequiresAction($paymentIntent)
    {
        $transaction = PaymentTransaction::where('gateway_payment_intent_id', $paymentIntent->id)->first();

        if (!$transaction) {
            Log::warning('Stripe webhook: Payment intent not found in database', [
                'payment_intent_id' => $paymentIntent->id
            ]);
            return;
        }

        // Update transaction status
        $transaction->update([
            'status' => 'requires_action',
            'gateway_response' => $paymentIntent->toArray(),
        ]);

        Log::info('Payment intent requires action', [
            'transaction_id' => $transaction->id,
            'payment_intent_id' => $paymentIntent->id
        ]);
    }

    /**
     * Handle charge dispute created
     */
    private function handleChargeDisputeCreated($dispute)
    {
        $chargeId = $dispute->charge;
        
        // Find transaction by charge ID
        $transaction = PaymentTransaction::whereJsonContains('gateway_response->charges->data', [['id' => $chargeId]])
                                        ->first();

        if (!$transaction) {
            Log::warning('Stripe webhook: Transaction not found for dispute', [
                'dispute_id' => $dispute->id,
                'charge_id' => $chargeId
            ]);
            return;
        }

        // Create dispute record or update transaction
        $transaction->update([
            'metadata' => array_merge($transaction->metadata ?? [], [
                'dispute_id' => $dispute->id,
                'dispute_reason' => $dispute->reason,
                'dispute_status' => $dispute->status,
                'dispute_amount' => $dispute->amount,
                'dispute_created' => $dispute->created
            ])
        ]);

        // Send notification about dispute
        $this->sendDisputeNotification($transaction, $dispute);

        Log::info('Charge dispute created processed', [
            'transaction_id' => $transaction->id,
            'dispute_id' => $dispute->id
        ]);
    }

    /**
     * Handle account updated
     */
    private function handleAccountUpdated($account)
    {
        $gatewayAccount = PaymentGatewayAccount::where('account_id', $account->id)
                                              ->where('gateway_type', 'stripe')
                                              ->first();

        if (!$gatewayAccount) {
            Log::warning('Stripe webhook: Gateway account not found', [
                'stripe_account_id' => $account->id
            ]);
            return;
        }

        // Update account details
        $gatewayAccount->update([
            'account_status' => $account->details_submitted ? 'active' : 'pending',
            'account_details' => [
                'type' => $account->type,
                'country' => $account->country,
                'email' => $account->email,
                'details_submitted' => $account->details_submitted,
                'charges_enabled' => $account->charges_enabled,
                'payouts_enabled' => $account->payouts_enabled,
                'created' => $account->created
            ],
            'capabilities' => $account->capabilities->toArray(),
            'is_active' => $account->charges_enabled,
            'last_sync_at' => now(),
        ]);

        Log::info('Account updated processed', [
            'gateway_account_id' => $gatewayAccount->id,
            'stripe_account_id' => $account->id,
            'charges_enabled' => $account->charges_enabled
        ]);
    }

    /**
     * Handle successful payout
     */
    private function handlePayoutPaid($payout)
    {
        $gatewayAccount = PaymentGatewayAccount::where('account_id', $payout->destination)
                                              ->where('gateway_type', 'stripe')
                                              ->first();

        if (!$gatewayAccount) {
            Log::warning('Stripe webhook: Gateway account not found for payout', [
                'payout_id' => $payout->id,
                'destination' => $payout->destination
            ]);
            return;
        }

        // Send payout notification
        $this->sendPayoutNotification($gatewayAccount, $payout, 'payout_completed');

        Log::info('Payout paid processed', [
            'payout_id' => $payout->id,
            'amount' => $payout->amount,
            'business_id' => $gatewayAccount->business_id
        ]);
    }

    /**
     * Handle failed payout
     */
    private function handlePayoutFailed($payout)
    {
        $gatewayAccount = PaymentGatewayAccount::where('account_id', $payout->destination)
                                              ->where('gateway_type', 'stripe')
                                              ->first();

        if (!$gatewayAccount) {
            Log::warning('Stripe webhook: Gateway account not found for failed payout', [
                'payout_id' => $payout->id,
                'destination' => $payout->destination
            ]);
            return;
        }

        // Send payout failure notification
        $this->sendPayoutNotification($gatewayAccount, $payout, 'payout_failed');

        Log::info('Payout failed processed', [
            'payout_id' => $payout->id,
            'failure_code' => $payout->failure_code,
            'business_id' => $gatewayAccount->business_id
        ]);
    }

    /**
     * Create or update revenue record
     */
    private function createOrUpdateRevenueRecord(PaymentTransaction $transaction)
    {
        RevenueRecord::updateOrCreate(
            ['transaction_id' => $transaction->id],
            [
                'business_id' => $transaction->business_id,
                'owner_id' => $transaction->owner_id,
                'gateway_type' => 'stripe',
                'service_id' => $transaction->service_id,
                'customer_id' => $transaction->customer_id,
                'booking_id' => $transaction->booking_id,
                'gross_amount' => $transaction->gross_amount,
                'gateway_fees' => $transaction->gateway_fees,
                'platform_fees' => $transaction->platform_fees,
                'net_amount' => $transaction->net_amount,
                'currency' => $transaction->currency,
                'revenue_date' => $transaction->processed_at->toDateString(),
                'revenue_category' => 'service',
                'payment_method' => 'stripe_card',
                'status' => 'completed',
                'metadata' => [
                    'stripe_payment_intent_id' => $transaction->gateway_payment_intent_id,
                    'stripe_account_id' => $transaction->gatewayAccount->account_id
                ]
            ]
        );
    }

    /**
     * Send payment notification
     */
    private function sendPaymentNotification(PaymentTransaction $transaction, string $type)
    {
        $business = $transaction->business;
        $customer = $transaction->customer;

        $data = [
            'payment_id' => $transaction->id,
            'amount' => $transaction->gross_amount,
            'currency' => $transaction->currency,
            'customer_name' => $customer ? $customer->name : 'Unknown Customer',
            'transaction_id' => $transaction->transaction_id
        ];

        $this->notificationService->createPaymentNotification($business, $type, $data);
    }

    /**
     * Send dispute notification
     */
    private function sendDisputeNotification(PaymentTransaction $transaction, $dispute)
    {
        $business = $transaction->business;

        $data = [
            'dispute_id' => $dispute->id,
            'amount' => $dispute->amount / 100, // Convert from cents
            'currency' => strtoupper($dispute->currency),
            'reason' => $dispute->reason,
            'transaction_id' => $transaction->transaction_id
        ];

        $this->notificationService->createNotification(
            $business->id,
            $business->owner_id,
            'payment',
            'Payment Dispute Created',
            "A dispute has been created for transaction {$transaction->transaction_id}",
            $data,
            'high',
            'dispute',
            $dispute->id
        );
    }

    /**
     * Send payout notification
     */
    private function sendPayoutNotification(PaymentGatewayAccount $gatewayAccount, $payout, string $type)
    {
        $business = $gatewayAccount->business;

        $data = [
            'payout_id' => $payout->id,
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'arrival_date' => Carbon::createFromTimestamp($payout->arrival_date)->toDateString()
        ];

        $this->notificationService->createPaymentNotification($business, $type, $data);
    }
}
