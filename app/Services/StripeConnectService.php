<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\Account;
use Stripe\AccountLink;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Transfer;
use Stripe\Exception\ApiErrorException;
use App\Models\PaymentGatewayAccount;
use App\Models\PaymentTransaction;
use App\Models\RevenueRecord;
use App\Models\Business;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class StripeConnectService
{
    protected $stripeSecretKey;
    protected $stripePublishableKey;
    protected $clientId;

    public function __construct()
    {
        $this->stripeSecretKey = config('services.stripe.secret');
        $this->stripePublishableKey = config('services.stripe.key');
        $this->clientId = config('services.stripe.client_id');
        
        Stripe::setApiKey($this->stripeSecretKey);
    }

    /**
     * Create Stripe Connect account link for onboarding
     */
    public function createAccountLink(int $businessId, int $ownerId): array
    {
        try {
            // Check if account already exists
            $existingAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                                  ->where('gateway_type', 'stripe')
                                                  ->first();

            $stripeAccountId = null;

            if ($existingAccount && $existingAccount->account_id) {
                $stripeAccountId = $existingAccount->account_id;
            } else {
                // Create new Stripe Express account
                $account = Account::create([
                    'type' => 'express',
                    'country' => 'US', // This should be configurable
                    'email' => auth()->user()->email,
                    'capabilities' => [
                        'card_payments' => ['requested' => true],
                        'transfers' => ['requested' => true],
                    ],
                    'business_type' => 'individual', // This could be configurable
                    'metadata' => [
                        'business_id' => $businessId,
                        'owner_id' => $ownerId,
                        'platform' => 'bookkei'
                    ]
                ]);

                $stripeAccountId = $account->id;

                // Store or update the gateway account
                PaymentGatewayAccount::updateOrCreate(
                    [
                        'business_id' => $businessId,
                        'gateway_type' => 'stripe'
                    ],
                    [
                        'owner_id' => $ownerId,
                        'account_id' => $stripeAccountId,
                        'account_status' => 'pending',
                        'account_details' => [
                            'type' => $account->type,
                            'country' => $account->country,
                            'email' => $account->email,
                            'created' => $account->created
                        ],
                        'capabilities' => $account->capabilities->toArray(),
                        'is_active' => false,
                        'connected_at' => now(),
                    ]
                );
            }

            // Create account link for onboarding
            $accountLink = AccountLink::create([
                'account' => $stripeAccountId,
                'refresh_url' => route('owner.payments.stripe.refresh'),
                'return_url' => route('owner.payments.stripe.return'),
                'type' => 'account_onboarding',
            ]);

            return [
                'success' => true,
                'account_link_url' => $accountLink->url,
                'stripe_account_id' => $stripeAccountId
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Connect account link creation failed', [
                'business_id' => $businessId,
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode()
            ];
        }
    }

    /**
     * Handle successful account connection
     */
    public function handleAccountReturn(string $stripeAccountId): array
    {
        try {
            // Retrieve account details from Stripe
            $account = Account::retrieve($stripeAccountId);

            // Find the gateway account record
            $gatewayAccount = PaymentGatewayAccount::where('account_id', $stripeAccountId)
                                                  ->where('gateway_type', 'stripe')
                                                  ->first();

            if (!$gatewayAccount) {
                return [
                    'success' => false,
                    'error' => 'Gateway account not found'
                ];
            }

            // Update account status and details
            $gatewayAccount->update([
                'account_status' => $account->details_submitted ? 'active' : 'pending',
                'account_details' => [
                    'type' => $account->type,
                    'country' => $account->country,
                    'email' => $account->email,
                    'details_submitted' => $account->details_submitted,
                    'charges_enabled' => $account->charges_enabled,
                    'payouts_enabled' => $account->payouts_enabled,
                    'created' => $account->created
                ],
                'capabilities' => $account->capabilities->toArray(),
                'is_active' => $account->charges_enabled,
                'last_sync_at' => now(),
            ]);

            return [
                'success' => true,
                'account_status' => $account->details_submitted ? 'active' : 'pending',
                'charges_enabled' => $account->charges_enabled,
                'payouts_enabled' => $account->payouts_enabled
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe account return handling failed', [
                'stripe_account_id' => $stripeAccountId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process payment using Stripe Connect
     */
    public function processPayment(array $paymentData): array
    {
        try {
            $businessId = $paymentData['business_id'];
            $amount = $paymentData['amount']; // Amount in cents
            $currency = $paymentData['currency'] ?? 'usd';
            $paymentMethodId = $paymentData['payment_method_id'];
            $customerId = $paymentData['customer_id'] ?? null;
            $bookingId = $paymentData['booking_id'] ?? null;
            $serviceId = $paymentData['service_id'] ?? null;

            // Get the connected Stripe account
            $gatewayAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                                  ->where('gateway_type', 'stripe')
                                                  ->where('is_active', true)
                                                  ->first();

            if (!$gatewayAccount) {
                return [
                    'success' => false,
                    'error' => 'No active Stripe account found for this business'
                ];
            }

            // Calculate platform fee (configurable percentage)
            $platformFeeRate = config('services.stripe.platform_fee_rate', 0.025); // 2.5% default
            $platformFee = round($amount * $platformFeeRate);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount,
                'currency' => $currency,
                'payment_method' => $paymentMethodId,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('owner.payments.stripe.return'),
                'application_fee_amount' => $platformFee,
                'metadata' => [
                    'business_id' => $businessId,
                    'customer_id' => $customerId,
                    'booking_id' => $bookingId,
                    'service_id' => $serviceId,
                    'platform' => 'bookkei'
                ]
            ], [
                'stripe_account' => $gatewayAccount->account_id
            ]);

            // Calculate fees
            $stripeFee = $this->calculateStripeFee($amount);
            $netAmount = $amount - $stripeFee - $platformFee;

            // Create transaction record
            $transaction = PaymentTransaction::create([
                'business_id' => $businessId,
                'owner_id' => $gatewayAccount->owner_id,
                'transaction_id' => 'TXN' . date('Ymd') . strtoupper(substr($paymentIntent->id, -8)),
                'booking_id' => $bookingId,
                'customer_id' => $customerId,
                'service_id' => $serviceId,
                'gateway_account_id' => $gatewayAccount->id,
                'gateway_type' => 'stripe',
                'gateway_transaction_id' => $paymentIntent->id,
                'gateway_payment_intent_id' => $paymentIntent->id,
                'transaction_type' => 'payment',
                'payment_method' => 'card',
                'gross_amount' => $amount / 100, // Convert from cents
                'gateway_fees' => $stripeFee / 100,
                'platform_fees' => $platformFee / 100,
                'net_amount' => $netAmount / 100,
                'currency' => strtoupper($currency),
                'status' => $paymentIntent->status === 'succeeded' ? 'completed' : 'pending',
                'metadata' => [
                    'stripe_payment_intent_id' => $paymentIntent->id,
                    'stripe_account_id' => $gatewayAccount->account_id
                ],
                'gateway_response' => $paymentIntent->toArray(),
                'processed_at' => $paymentIntent->status === 'succeeded' ? now() : null,
                'gateway_created_at' => Carbon::createFromTimestamp($paymentIntent->created),
            ]);

            // Create revenue record if payment succeeded
            if ($paymentIntent->status === 'succeeded') {
                $this->createRevenueRecord($transaction);
            }

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'transaction' => $transaction,
                'status' => $paymentIntent->status,
                'requires_action' => $paymentIntent->status === 'requires_action',
                'client_secret' => $paymentIntent->client_secret
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe payment processing failed', [
                'business_id' => $paymentData['business_id'] ?? null,
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode()
            ];
        }
    }

    /**
     * Calculate Stripe processing fee
     */
    private function calculateStripeFee(int $amountInCents): int
    {
        // Stripe standard rate: 2.9% + 30¢ for US cards
        $percentageFee = round($amountInCents * 0.029);
        $fixedFee = 30; // 30 cents in cents
        
        return $percentageFee + $fixedFee;
    }

    /**
     * Create revenue record from transaction
     */
    private function createRevenueRecord(PaymentTransaction $transaction): void
    {
        RevenueRecord::create([
            'business_id' => $transaction->business_id,
            'owner_id' => $transaction->owner_id,
            'transaction_id' => $transaction->id,
            'gateway_type' => 'stripe',
            'service_id' => $transaction->service_id,
            'customer_id' => $transaction->customer_id,
            'booking_id' => $transaction->booking_id,
            'gross_amount' => $transaction->gross_amount,
            'gateway_fees' => $transaction->gateway_fees,
            'platform_fees' => $transaction->platform_fees,
            'net_amount' => $transaction->net_amount,
            'currency' => $transaction->currency,
            'revenue_date' => $transaction->processed_at->toDateString(),
            'revenue_category' => 'service',
            'payment_method' => 'stripe_card',
            'status' => 'completed',
            'metadata' => [
                'stripe_payment_intent_id' => $transaction->gateway_payment_intent_id,
                'stripe_account_id' => $transaction->gatewayAccount->account_id
            ]
        ]);
    }

    /**
     * Process refund
     */
    public function processRefund(PaymentTransaction $transaction, float $amount = null): array
    {
        try {
            $refundAmount = $amount ? round($amount * 100) : round($transaction->gross_amount * 100);

            $refund = Refund::create([
                'payment_intent' => $transaction->gateway_payment_intent_id,
                'amount' => $refundAmount,
                'metadata' => [
                    'business_id' => $transaction->business_id,
                    'original_transaction_id' => $transaction->id,
                    'platform' => 'bookkei'
                ]
            ], [
                'stripe_account' => $transaction->gatewayAccount->account_id
            ]);

            // Create refund transaction record
            $refundTransaction = PaymentTransaction::create([
                'business_id' => $transaction->business_id,
                'owner_id' => $transaction->owner_id,
                'booking_id' => $transaction->booking_id,
                'customer_id' => $transaction->customer_id,
                'service_id' => $transaction->service_id,
                'gateway_account_id' => $transaction->gateway_account_id,
                'gateway_type' => 'stripe',
                'gateway_transaction_id' => $refund->id,
                'transaction_type' => $amount ? 'partial_refund' : 'refund',
                'payment_method' => 'card',
                'gross_amount' => -($refundAmount / 100),
                'gateway_fees' => 0,
                'platform_fees' => 0,
                'net_amount' => -($refundAmount / 100),
                'currency' => $transaction->currency,
                'status' => 'completed',
                'metadata' => [
                    'stripe_refund_id' => $refund->id,
                    'original_payment_intent_id' => $transaction->gateway_payment_intent_id
                ],
                'gateway_response' => $refund->toArray(),
                'processed_at' => now(),
                'gateway_created_at' => Carbon::createFromTimestamp($refund->created),
            ]);

            return [
                'success' => true,
                'refund' => $refund,
                'refund_transaction' => $refundTransaction
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe refund processing failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Disconnect Stripe account
     */
    public function disconnectAccount(int $businessId): array
    {
        try {
            $gatewayAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                                  ->where('gateway_type', 'stripe')
                                                  ->first();

            if (!$gatewayAccount) {
                return [
                    'success' => false,
                    'error' => 'No Stripe account found for this business'
                ];
            }

            // Deauthorize the account (this removes the connection)
            // Note: In production, you might want to keep the account record for historical purposes
            $gatewayAccount->update([
                'is_active' => false,
                'account_status' => 'disconnected',
                'last_sync_at' => now(),
            ]);

            return [
                'success' => true,
                'message' => 'Stripe account disconnected successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Stripe account disconnection failed', [
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get account status and capabilities
     */
    public function getAccountStatus(string $stripeAccountId): array
    {
        try {
            $account = Account::retrieve($stripeAccountId);

            return [
                'success' => true,
                'account' => [
                    'id' => $account->id,
                    'charges_enabled' => $account->charges_enabled,
                    'payouts_enabled' => $account->payouts_enabled,
                    'details_submitted' => $account->details_submitted,
                    'type' => $account->type,
                    'country' => $account->country,
                    'capabilities' => $account->capabilities->toArray(),
                    'requirements' => $account->requirements->toArray()
                ]
            ];

        } catch (ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync account data from Stripe
     */
    public function syncAccountData(int $businessId): array
    {
        try {
            $gatewayAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                                  ->where('gateway_type', 'stripe')
                                                  ->first();

            if (!$gatewayAccount || !$gatewayAccount->account_id) {
                return [
                    'success' => false,
                    'error' => 'No Stripe account found'
                ];
            }

            $account = Account::retrieve($gatewayAccount->account_id);

            $gatewayAccount->update([
                'account_status' => $account->details_submitted ? 'active' : 'pending',
                'account_details' => [
                    'type' => $account->type,
                    'country' => $account->country,
                    'email' => $account->email,
                    'details_submitted' => $account->details_submitted,
                    'charges_enabled' => $account->charges_enabled,
                    'payouts_enabled' => $account->payouts_enabled,
                    'created' => $account->created
                ],
                'capabilities' => $account->capabilities->toArray(),
                'is_active' => $account->charges_enabled,
                'last_sync_at' => now(),
            ]);

            return [
                'success' => true,
                'account_status' => $account->details_submitted ? 'active' : 'pending',
                'charges_enabled' => $account->charges_enabled,
                'payouts_enabled' => $account->payouts_enabled
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe account sync failed', [
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
