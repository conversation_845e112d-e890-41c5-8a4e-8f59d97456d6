<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinancialMetric extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'metric_date',
        'total_revenue',
        'total_transactions',
        'average_transaction_value',
        'gateway_fees_total',
        'platform_fees_total',
        'net_revenue',
        'customer_count',
        'new_customer_count',
        'returning_customer_count',
        'refund_amount',
        'chargeback_amount',
        'revenue_growth_rate',
        'payment_method_breakdown',
        'service_revenue_breakdown',
    ];

    protected $casts = [
        'metric_date' => 'date',
        'total_revenue' => 'decimal:2',
        'average_transaction_value' => 'decimal:2',
        'gateway_fees_total' => 'decimal:2',
        'platform_fees_total' => 'decimal:2',
        'net_revenue' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'chargeback_amount' => 'decimal:2',
        'revenue_growth_rate' => 'decimal:4',
        'payment_method_breakdown' => 'array',
        'service_revenue_breakdown' => 'array',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    // Scopes
    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('metric_date', [$startDate, $endDate]);
    }
}
