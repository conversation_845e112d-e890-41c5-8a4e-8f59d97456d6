<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessFinancialConfig extends Model
{
    use HasFactory;

    protected $table = 'business_financial_config';

    protected $fillable = [
        'business_id',
        'owner_id',
        'default_currency',
        'tax_rate',
        'platform_fee_rate',
        'revenue_goals',
        'pricing_strategy_default',
        'payment_gateway_preferences',
        'financial_year_start',
        'accounting_method',
    ];

    protected $casts = [
        'tax_rate' => 'decimal:4',
        'platform_fee_rate' => 'decimal:4',
        'revenue_goals' => 'array',
        'payment_gateway_preferences' => 'array',
        'financial_year_start' => 'date',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    // Scopes
    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    // Accessors
    public function getFormattedTaxRateAttribute()
    {
        return $this->tax_rate ? ($this->tax_rate * 100) . '%' : 'N/A';
    }

    public function getFormattedPlatformFeeRateAttribute()
    {
        return $this->platform_fee_rate ? ($this->platform_fee_rate * 100) . '%' : '0%';
    }
}
