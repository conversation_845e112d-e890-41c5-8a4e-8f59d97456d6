<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'service_category_id',
        'name',
        'slug',
        'description',
        'short_description',
        'duration_minutes',
        'base_price',
        'deposit_amount',
        'deposit_required',
        'buffer_time_before',
        'buffer_time_after',
        'max_advance_booking_days',
        'min_advance_booking_hours',
        'max_participants',
        'pricing_variables',
        'booking_rules',
        'online_booking_enabled',
        'requires_approval',
        'sort_order',
        'is_active',
        // Landing Page Display Fields
        'is_public',
        'featured_on_landing',
        'landing_display_order',
        'landing_display_config',
        'show_price_on_landing',
        'show_duration_on_landing',
        'show_description_on_landing',
        'show_image_on_landing',
        'landing_page_title',
        'landing_page_description',
        'landing_page_keywords',
        'quick_booking_enabled',

        // Advanced Service Features
        'service_packages',
        'add_on_services',
        'service_prerequisites',
        'staff_assignments',
        'equipment_requirements',
        'facility_requirements',
        'seasonal_availability',
        'seasonal_pricing',
        'is_seasonal',
        'season_start_date',
        'season_end_date',
        'preparation_instructions',
        'aftercare_instructions',
        'cancellation_policy',
        'service_faqs',
        'before_after_gallery',
        'service_policies',
        'related_services',
        'cross_sell_services',
        'upsell_services',
        'booking_button_text',
        'booking_button_color',
        'landing_page_views',
        'landing_page_clicks',
        'last_landing_view',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'deposit_required' => 'boolean',
        'duration_minutes' => 'integer',
        'buffer_time_before' => 'integer',
        'buffer_time_after' => 'integer',
        'max_advance_booking_days' => 'integer',
        'min_advance_booking_hours' => 'integer',
        'max_participants' => 'integer',
        'pricing_variables' => 'array',
        'booking_rules' => 'array',
        'online_booking_enabled' => 'boolean',
        'requires_approval' => 'boolean',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        // Landing Page Display Casts
        'is_public' => 'boolean',
        'featured_on_landing' => 'boolean',
        'landing_display_config' => 'array',
        'show_price_on_landing' => 'boolean',
        'show_duration_on_landing' => 'boolean',
        'show_description_on_landing' => 'boolean',
        'show_image_on_landing' => 'boolean',
        'quick_booking_enabled' => 'boolean',

        // Advanced Service Features Casts
        'service_packages' => 'array',
        'add_on_services' => 'array',
        'service_prerequisites' => 'array',
        'staff_assignments' => 'array',
        'equipment_requirements' => 'array',
        'facility_requirements' => 'array',
        'seasonal_availability' => 'array',
        'seasonal_pricing' => 'array',
        'is_seasonal' => 'boolean',
        'season_start_date' => 'date',
        'season_end_date' => 'date',
        'service_faqs' => 'array',
        'before_after_gallery' => 'array',
        'service_policies' => 'array',
        'related_services' => 'array',
        'cross_sell_services' => 'array',
        'upsell_services' => 'array',
        'landing_page_views' => 'integer',
        'landing_page_clicks' => 'integer',
        'last_landing_view' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->name);
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'service_category_id');
    }

    public function resources(): BelongsToMany
    {
        return $this->belongsToMany(Resource::class, 'service_resources')
                    ->withPivot('quantity_required', 'is_required', 'setup_time_minutes', 'cleanup_time_minutes')
                    ->withTimestamps();
    }

    public function requiredResources(): BelongsToMany
    {
        return $this->resources()->wherePivot('is_required', true);
    }

    public function addons(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'service_addons', 'service_id', 'addon_service_id')
                    ->withPivot('addon_price', 'is_required', 'max_quantity')
                    ->withTimestamps();
    }

    public function parentServices(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'service_addons', 'addon_service_id', 'service_id')
                    ->withPivot('addon_price', 'is_required', 'max_quantity')
                    ->withTimestamps();
    }

    public function bookingServices(): HasMany
    {
        return $this->hasMany(BookingService::class);
    }

    public function waitingLists(): HasMany
    {
        return $this->hasMany(WaitingList::class);
    }

    // Revenue relationship
    public function revenueRecords(): HasMany
    {
        return $this->hasMany(RevenueRecord::class);
    }

    // Favorites relationships
    public function favorites(): HasMany
    {
        return $this->hasMany(CustomerFavorite::class);
    }

    public function favoritedByUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'customer_favorites')
                    ->withTimestamps()
                    ->orderBy('customer_favorites.created_at', 'desc');
    }

    /**
     * Get the count of users who have favorited this service.
     */
    public function getFavoritesCountAttribute(): int
    {
        return $this->favorites()->count();
    }

    /**
     * Check if a specific user has favorited this service.
     */
    public function isFavoritedBy($userId): bool
    {
        return $this->favorites()->where('user_id', $userId)->exists();
    }

    // Reviews relationships
    public function reviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class);
    }

    public function approvedReviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class)->where('is_approved', true);
    }

    public function featuredReviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class)->where('is_featured', true);
    }

    /**
     * Get the average rating for this service.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->approvedReviews()->avg('rating') ?? 0;
    }

    /**
     * Get the total number of reviews for this service.
     */
    public function getReviewsCountAttribute(): int
    {
        return $this->approvedReviews()->count();
    }

    /**
     * Get the rating distribution for this service.
     */
    public function getRatingDistributionAttribute(): array
    {
        $distribution = [];

        for ($i = 1; $i <= 5; $i++) {
            $count = $this->approvedReviews()->where('rating', $i)->count();
            $distribution[$i] = $count;
        }

        return $distribution;
    }

    /**
     * Get formatted average rating with stars.
     */
    public function getFormattedRatingAttribute(): string
    {
        $rating = $this->average_rating;
        $fullStars = floor($rating);
        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
        $emptyStars = 5 - $fullStars - $halfStar;

        return str_repeat('★', $fullStars) .
               str_repeat('☆', $halfStar) .
               str_repeat('☆', $emptyStars);
    }

    // Availability relationships
    public function availabilitySlots(): HasMany
    {
        return $this->hasMany(ServiceAvailability::class);
    }

    public function availableSlots(): HasMany
    {
        return $this->hasMany(ServiceAvailability::class)
                    ->where('is_available', true)
                    ->where('current_bookings', '<', 'max_bookings');
    }

    public function futureAvailableSlots(): HasMany
    {
        return $this->availableSlots()->future();
    }

    /**
     * Get available time slots for a specific date.
     */
    public function getAvailableSlotsForDate($date): \Illuminate\Database\Eloquent\Collection
    {
        return $this->availableSlots()
                    ->forDate($date)
                    ->future()
                    ->orderBy('start_time')
                    ->get();
    }

    /**
     * Check if service is available on a specific date.
     */
    public function isAvailableOnDate($date): bool
    {
        return $this->getAvailableSlotsForDate($date)->isNotEmpty();
    }

    /**
     * Check if service is available today.
     */
    public function isAvailableToday(): bool
    {
        return $this->isAvailableOnDate(now()->toDateString());
    }

    /**
     * Check if service has special offers.
     */
    public function hasSpecialOffer(): bool
    {
        // Check if service has advance booking discount
        if (isset($this->pricing_variables['advance_booking_discount']) && $this->pricing_variables['advance_booking_discount'] > 0) {
            return true;
        }

        // Check if service has seasonal pricing with discounts
        if (isset($this->pricing_variables['enable_seasonal']) && $this->pricing_variables['enable_seasonal']) {
            $seasonalPricing = $this->pricing_variables['seasonal_pricing'] ?? [];
            foreach ($seasonalPricing as $season) {
                if (isset($season['discount']) && $season['discount'] > 0) {
                    return true;
                }
            }
        }

        // Check if service has group discounts
        if (isset($this->pricing_variables['group_discount']) && $this->pricing_variables['group_discount'] > 0) {
            return true;
        }

        // Check if service has time-based discounts
        if (isset($this->pricing_variables['time_based_pricing'])) {
            $timePricing = $this->pricing_variables['time_based_pricing'] ?? [];
            foreach ($timePricing as $timeSlot) {
                if (isset($timeSlot['discount']) && $timeSlot['discount'] > 0) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get the next available slot for this service.
     */
    public function getNextAvailableSlot(): ?ServiceAvailability
    {
        return $this->futureAvailableSlots()
                    ->orderBy('date')
                    ->orderBy('start_time')
                    ->first();
    }

    /**
     * Generate availability slots for the next N days.
     */
    public function generateAvailabilitySlots($days = 30, $startTime = '09:00', $endTime = '17:00', $slotDuration = 60)
    {
        $startDate = now()->toDateString();

        for ($i = 0; $i < $days; $i++) {
            $date = now()->addDays($i)->toDateString();

            // Skip if slots already exist for this date
            if ($this->availabilitySlots()->forDate($date)->exists()) {
                continue;
            }

            ServiceAvailability::generateSlotsForService(
                $this->id,
                $this->business_id,
                $date,
                $startTime,
                $endTime,
                $slotDuration
            );
        }
    }

    // Image relationships
    public function images(): HasMany
    {
        return $this->hasMany(ServiceImage::class);
    }

    public function activeImages(): HasMany
    {
        return $this->hasMany(ServiceImage::class)->where('is_active', true);
    }

    public function primaryImage(): HasMany
    {
        return $this->hasMany(ServiceImage::class)->where('is_primary', true);
    }

    public function featuredImages(): HasMany
    {
        return $this->hasMany(ServiceImage::class)->where('is_featured', true);
    }

    /**
     * Get the primary image for this service.
     */
    public function getPrimaryImageAttribute(): ?ServiceImage
    {
        return $this->activeImages()->where('is_primary', true)->first();
    }

    /**
     * Get the primary image URL or a default placeholder.
     */
    public function getPrimaryImageUrlAttribute(): string
    {
        $primaryImage = $this->primary_image;

        if ($primaryImage) {
            return $primaryImage->full_url;
        }

        // Return a default placeholder image
        return asset('images/service-placeholder.jpg');
    }

    /**
     * Get the thumbnail URL for the primary image.
     */
    public function getThumbnailUrlAttribute(): string
    {
        $primaryImage = $this->primary_image;

        if ($primaryImage) {
            return $primaryImage->thumbnail_url;
        }

        // Return a default placeholder thumbnail
        return asset('images/service-placeholder-thumb.jpg');
    }

    /**
     * Get all active images ordered by sort order.
     */
    public function getOrderedImagesAttribute(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->activeImages()->ordered()->get();
    }

    /**
     * Check if service has images.
     */
    public function hasImages(): bool
    {
        return $this->activeImages()->exists();
    }

    /**
     * Get image gallery for display.
     */
    public function getImageGalleryAttribute(): array
    {
        return $this->ordered_images->map(function ($image) {
            return [
                'id' => $image->id,
                'url' => $image->full_url,
                'thumbnail' => $image->thumbnail_url,
                'medium' => $image->medium_url,
                'alt' => $image->alt_text ?: $this->name,
                'description' => $image->description,
                'is_primary' => $image->is_primary,
                'dimensions' => $image->dimensions,
            ];
        })->toArray();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOnlineBookingEnabled($query)
    {
        return $query->where('online_booking_enabled', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('service_category_id', $categoryId);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeFeaturedOnLanding($query)
    {
        return $query->where('featured_on_landing', true);
    }

    public function scopeVisibleOnLanding($query)
    {
        return $query->where('is_active', true)->where('is_public', true);
    }

    public function scopeOrderedForLanding($query)
    {
        return $query->orderBy('landing_display_order')->orderBy('sort_order');
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getTotalDurationAttribute()
    {
        return $this->duration_minutes + $this->buffer_time_before + $this->buffer_time_after;
    }

    public function getPriceAttribute()
    {
        return $this->base_price;
    }

    public function getFormattedPriceAttribute()
    {
        return number_format($this->base_price, 2);
    }

    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function calculatePrice($participants = 1, $variables = [])
    {
        $basePrice = $this->base_price;

        if (!$this->pricing_variables) {
            return $basePrice;
        }

        // Apply participant-based pricing
        $participantType = $this->pricing_variables['participant_type'] ?? 'fixed';

        if ($participantType === 'per_participant') {
            $basePrice *= $participants;
        } elseif ($participantType === 'group_rate' && $participants >= ($this->pricing_variables['group_discount_threshold'] ?? 3)) {
            $discount = ($this->pricing_variables['group_discount_percent'] ?? 10) / 100;
            $basePrice = $basePrice * (1 - $discount);
        }

        // Apply time-based pricing
        if (isset($variables['booking_time']) && isset($this->pricing_variables['time_based_pricing'])) {
            $basePrice = $this->applyTimePricing($basePrice, $variables['booking_time']);
        }

        // Apply seasonal pricing
        if (isset($variables['booking_date']) && isset($this->pricing_variables['enable_seasonal']) && $this->pricing_variables['enable_seasonal']) {
            $basePrice = $this->applySeasonalPricing($basePrice, $variables['booking_date']);
        }

        // Apply advance booking discount
        if (isset($variables['booking_date']) && isset($this->pricing_variables['advance_booking_days'])) {
            $basePrice = $this->applyAdvanceBookingDiscount($basePrice, $variables['booking_date']);
        }

        return $basePrice;
    }

    protected function applyTimePricing($price, $bookingTime)
    {
        $timeBased = $this->pricing_variables['time_based_pricing'] ?? 'none';

        if ($timeBased === 'none') {
            return $price;
        }

        $peakStart = $this->pricing_variables['peak_start_time'] ?? '18:00';
        $peakEnd = $this->pricing_variables['peak_end_time'] ?? '22:00';
        $adjustment = ($this->pricing_variables['peak_adjustment'] ?? 20) / 100;

        $bookingHour = date('H:i', strtotime($bookingTime));
        $isPeakTime = $bookingHour >= $peakStart && $bookingHour <= $peakEnd;

        if ($timeBased === 'peak_hours' && $isPeakTime) {
            return $price * (1 + $adjustment);
        } elseif ($timeBased === 'off_peak' && !$isPeakTime) {
            return $price * (1 - $adjustment);
        }

        return $price;
    }

    protected function applySeasonalPricing($price, $bookingDate)
    {
        $month = date('n', strtotime($bookingDate));
        $adjustment = 0;

        // Winter: Dec, Jan, Feb
        if (in_array($month, [12, 1, 2])) {
            $adjustment = ($this->pricing_variables['winter_adjustment'] ?? 0) / 100;
        }
        // Spring: Mar, Apr, May
        elseif (in_array($month, [3, 4, 5])) {
            $adjustment = ($this->pricing_variables['spring_adjustment'] ?? 0) / 100;
        }
        // Summer: Jun, Jul, Aug
        elseif (in_array($month, [6, 7, 8])) {
            $adjustment = ($this->pricing_variables['summer_adjustment'] ?? 0) / 100;
        }
        // Fall: Sep, Oct, Nov
        elseif (in_array($month, [9, 10, 11])) {
            $adjustment = ($this->pricing_variables['fall_adjustment'] ?? 0) / 100;
        }

        return $price * (1 + $adjustment);
    }

    protected function applyAdvanceBookingDiscount($price, $bookingDate)
    {
        $advanceDays = $this->pricing_variables['advance_booking_days'] ?? 7;
        $discount = ($this->pricing_variables['advance_booking_discount'] ?? 5) / 100;

        $daysInAdvance = (strtotime($bookingDate) - time()) / (60 * 60 * 24);

        if ($daysInAdvance >= $advanceDays) {
            return $price * (1 - $discount);
        }

        return $price;
    }

    // Additional helper methods
    public function isOnlineBookable()
    {
        return $this->is_active && $this->online_booking_enabled;
    }

    public function requiresDeposit()
    {
        return $this->deposit_required && $this->deposit_amount > 0;
    }

    public function requiresApproval()
    {
        return $this->requires_approval;
    }

    public function canBookInAdvance($date)
    {
        if (!$this->max_advance_booking_days) {
            return true;
        }

        $maxDate = now()->addDays($this->max_advance_booking_days);
        return $date <= $maxDate;
    }

    public function hasMinimumAdvanceNotice($date)
    {
        if (!$this->min_advance_booking_hours) {
            return true;
        }

        $minDate = now()->addHours($this->min_advance_booking_hours);
        return $date >= $minDate;
    }

    public function isAvailableForBooking($date = null)
    {
        if (!$this->is_active) {
            return false;
        }

        if (!$this->online_booking_enabled) {
            return false;
        }

        if ($date) {
            if (!$this->canBookInAdvance($date)) {
                return false;
            }

            if (!$this->hasMinimumAdvanceNotice($date)) {
                return false;
            }
        }

        return true;
    }

    public function getBookingRules()
    {
        return [
            'max_advance_days' => $this->max_advance_booking_days,
            'min_advance_hours' => $this->min_advance_booking_hours,
            'max_participants' => $this->max_participants,
            'requires_deposit' => $this->requiresDeposit(),
            'deposit_amount' => $this->deposit_amount,
            'requires_approval' => $this->requires_approval,
            'buffer_before' => $this->buffer_time_before,
            'buffer_after' => $this->buffer_time_after,
        ];
    }

    // Landing Page Methods
    public function incrementLandingPageView()
    {
        $this->increment('landing_page_views');
        $this->update(['last_landing_view' => now()]);
    }

    public function incrementLandingPageClick()
    {
        $this->increment('landing_page_clicks');
    }

    public function getLandingPageTitle()
    {
        return $this->landing_page_title ?: $this->name;
    }

    public function getLandingPageDescription()
    {
        return $this->landing_page_description ?: $this->short_description ?: $this->description;
    }

    public function getLandingPageKeywords()
    {
        if ($this->landing_page_keywords) {
            return $this->landing_page_keywords;
        }

        $keywords = [$this->name];
        if ($this->category) {
            $keywords[] = $this->category->name;
        }
        if ($this->business) {
            $keywords[] = $this->business->name;
        }

        return implode(', ', $keywords);
    }

    public function getBookingButtonText()
    {
        return $this->booking_button_text ?: 'Book Now';
    }

    public function getBookingButtonColor()
    {
        return $this->booking_button_color ?: '#007bff';
    }

    public function shouldShowOnLanding($displayConfig = [])
    {
        if (!$this->is_active || !$this->is_public) {
            return false;
        }

        // Check if service is in hidden services list
        if (isset($displayConfig['hidden_services']) && in_array($this->id, $displayConfig['hidden_services'])) {
            return false;
        }

        return true;
    }

    public function getLandingDisplayConfig()
    {
        $defaultConfig = [
            'show_price' => $this->show_price_on_landing,
            'show_duration' => $this->show_duration_on_landing,
            'show_description' => $this->show_description_on_landing,
            'show_image' => $this->show_image_on_landing,
            'quick_booking' => $this->quick_booking_enabled,
        ];

        if ($this->landing_display_config) {
            return array_merge($defaultConfig, $this->landing_display_config);
        }

        return $defaultConfig;
    }

    public function generateServiceSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $this->name,
            'description' => $this->getLandingPageDescription(),
            'provider' => [
                '@type' => 'Organization',
                'name' => $this->business->name,
            ],
        ];

        if ($this->base_price) {
            $schema['offers'] = [
                '@type' => 'Offer',
                'price' => $this->base_price,
                'priceCurrency' => $this->business->currency ?? 'USD',
            ];
        }

        if ($this->duration_minutes) {
            $schema['duration'] = 'PT' . $this->duration_minutes . 'M';
        }

        if ($this->primary_image_url) {
            $schema['image'] = $this->primary_image_url;
        }

        return $schema;
    }
}
