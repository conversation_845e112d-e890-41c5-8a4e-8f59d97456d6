<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PricingStrategy extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'service_id',
        'strategy_name',
        'strategy_type',
        'base_price',
        'minimum_price',
        'maximum_price',
        'pricing_rules',
        'seasonal_adjustments',
        'discount_rules',
        'package_pricing',
        'tier_pricing',
        'effective_from',
        'effective_to',
        'is_active',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'minimum_price' => 'decimal:2',
        'maximum_price' => 'decimal:2',
        'pricing_rules' => 'array',
        'seasonal_adjustments' => 'array',
        'discount_rules' => 'array',
        'package_pricing' => 'array',
        'tier_pricing' => 'array',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function experiments(): HasMany
    {
        return $this->hasMany(PricingExperiment::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeForService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }

    public function scopeEffectiveNow($query)
    {
        $now = now()->toDateString();
        return $query->where(function($q) use ($now) {
            $q->where('effective_from', '<=', $now)
              ->where(function($subQ) use ($now) {
                  $subQ->whereNull('effective_to')
                       ->orWhere('effective_to', '>=', $now);
              });
        });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('strategy_type', $type);
    }

    // Accessors
    public function getFormattedBasePriceAttribute()
    {
        return number_format($this->base_price, 2);
    }

    public function getCurrentPriceAttribute()
    {
        return $this->calculatePrice();
    }

    public function getIsEffectiveAttribute()
    {
        $now = now()->toDateString();
        $fromCheck = !$this->effective_from || $this->effective_from <= $now;
        $toCheck = !$this->effective_to || $this->effective_to >= $now;

        return $this->is_active && $fromCheck && $toCheck;
    }

    // Price calculation methods
    public function calculatePrice($date = null, $context = [])
    {
        $date = $date ? Carbon::parse($date) : now();
        $basePrice = $this->base_price;

        switch ($this->strategy_type) {
            case 'fixed':
                return $basePrice;

            case 'dynamic':
                return $this->calculateDynamicPrice($basePrice, $context);

            case 'seasonal':
                return $this->calculateSeasonalPrice($basePrice, $date);

            case 'demand_based':
                return $this->calculateDemandBasedPrice($basePrice, $date, $context);

            case 'package':
                return $this->calculatePackagePrice($basePrice, $context);

            case 'tier_based':
                return $this->calculateTierBasedPrice($basePrice, $context);

            default:
                return $basePrice;
        }
    }

    private function calculateDynamicPrice($basePrice, $context)
    {
        $price = $basePrice;

        if ($this->pricing_rules) {
            foreach ($this->pricing_rules as $rule) {
                $price = $this->applyPricingRule($price, $rule, $context);
            }
        }

        return $this->enforceMinMaxLimits($price);
    }

    private function calculateSeasonalPrice($basePrice, $date)
    {
        $price = $basePrice;

        if ($this->seasonal_adjustments) {
            $month = $date->month;
            $dayOfWeek = $date->dayOfWeek;

            foreach ($this->seasonal_adjustments as $adjustment) {
                if ($this->isSeasonalAdjustmentApplicable($adjustment, $month, $dayOfWeek)) {
                    $price = $this->applySeasonalAdjustment($price, $adjustment);
                }
            }
        }

        return $this->enforceMinMaxLimits($price);
    }

    private function calculateDemandBasedPrice($basePrice, $date, $context)
    {
        // Get booking demand for the service around this date
        $demand = $this->getBookingDemand($date, $context);

        $price = $basePrice;

        // Increase price based on demand
        if ($demand['high_demand']) {
            $multiplier = 1 + ($demand['demand_factor'] * 0.2); // Up to 20% increase
            $price *= $multiplier;
        } elseif ($demand['low_demand']) {
            $multiplier = 1 - ($demand['availability_factor'] * 0.1); // Up to 10% decrease
            $price *= $multiplier;
        }

        return $this->enforceMinMaxLimits($price);
    }

    private function calculatePackagePrice($basePrice, $context)
    {
        $quantity = $context['quantity'] ?? 1;

        if ($this->package_pricing && $quantity > 1) {
            foreach ($this->package_pricing as $package) {
                if ($quantity >= $package['min_quantity']) {
                    $discount = $package['discount_percentage'] ?? 0;
                    return $basePrice * $quantity * (1 - $discount / 100);
                }
            }
        }

        return $basePrice * $quantity;
    }

    private function calculateTierBasedPrice($basePrice, $context)
    {
        $customerTier = $context['customer_tier'] ?? 'standard';

        if ($this->tier_pricing && isset($this->tier_pricing[$customerTier])) {
            $tierConfig = $this->tier_pricing[$customerTier];
            $discount = $tierConfig['discount_percentage'] ?? 0;
            return $basePrice * (1 - $discount / 100);
        }

        return $basePrice;
    }

    private function applyPricingRule($price, $rule, $context)
    {
        // Apply different pricing rules based on conditions
        $condition = $rule['condition'] ?? [];
        $adjustment = $rule['adjustment'] ?? [];

        if ($this->evaluateCondition($condition, $context)) {
            if ($adjustment['type'] === 'percentage') {
                $price *= (1 + $adjustment['value'] / 100);
            } elseif ($adjustment['type'] === 'fixed') {
                $price += $adjustment['value'];
            }
        }

        return $price;
    }

    private function applySeasonalAdjustment($price, $adjustment)
    {
        $type = $adjustment['type'] ?? 'percentage';
        $value = $adjustment['value'] ?? 0;

        if ($type === 'percentage') {
            return $price * (1 + $value / 100);
        } elseif ($type === 'fixed') {
            return $price + $value;
        }

        return $price;
    }

    private function isSeasonalAdjustmentApplicable($adjustment, $month, $dayOfWeek)
    {
        $months = $adjustment['months'] ?? [];
        $daysOfWeek = $adjustment['days_of_week'] ?? [];

        $monthMatch = empty($months) || in_array($month, $months);
        $dayMatch = empty($daysOfWeek) || in_array($dayOfWeek, $daysOfWeek);

        return $monthMatch && $dayMatch;
    }

    private function evaluateCondition($condition, $context)
    {
        // Evaluate pricing rule conditions
        foreach ($condition as $key => $value) {
            if (!isset($context[$key]) || $context[$key] !== $value) {
                return false;
            }
        }
        return true;
    }

    private function getBookingDemand($date, $context)
    {
        // Simplified demand calculation - in a real implementation,
        // this would analyze booking patterns, availability, etc.
        $serviceId = $this->service_id;
        $dateRange = [
            $date->copy()->subDays(3),
            $date->copy()->addDays(3)
        ];

        // Get booking count for the period
        $bookingCount = \App\Models\Booking::where('service_id', $serviceId)
                                         ->whereBetween('start_datetime', $dateRange)
                                         ->count();

        // Simple demand calculation
        $averageBookings = 5; // This should be calculated from historical data
        $demandFactor = $bookingCount / max($averageBookings, 1);

        return [
            'booking_count' => $bookingCount,
            'demand_factor' => min($demandFactor, 2), // Cap at 2x
            'high_demand' => $demandFactor > 1.5,
            'low_demand' => $demandFactor < 0.5,
            'availability_factor' => max(0, 1 - $demandFactor)
        ];
    }

    private function enforceMinMaxLimits($price)
    {
        if ($this->minimum_price && $price < $this->minimum_price) {
            return $this->minimum_price;
        }

        if ($this->maximum_price && $price > $this->maximum_price) {
            return $this->maximum_price;
        }

        return $price;
    }

    // Static methods
    public static function getActiveForService($serviceId, $businessId)
    {
        return static::where('service_id', $serviceId)
                    ->where('business_id', $businessId)
                    ->active()
                    ->effectiveNow()
                    ->first();
    }

    public static function calculateServicePrice($serviceId, $businessId, $context = [])
    {
        $strategy = static::getActiveForService($serviceId, $businessId);

        if (!$strategy) {
            // Fallback to service base price
            $service = \App\Models\Service::find($serviceId);
            return $service ? $service->base_price : 0;
        }

        return $strategy->calculatePrice(null, $context);
    }
}
