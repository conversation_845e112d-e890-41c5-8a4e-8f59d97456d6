<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinancialAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'alert_type',
        'title',
        'message',
        'severity',
        'alert_data',
        'is_read',
        'triggered_at',
        'read_at',
    ];

    protected $casts = [
        'alert_data' => 'array',
        'is_read' => 'boolean',
        'triggered_at' => 'datetime',
        'read_at' => 'datetime',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    // Scopes
    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('alert_type', $type);
    }

    // Accessors
    public function getSeverityBadgeAttribute()
    {
        $severityClasses = [
            'low' => 'info',
            'medium' => 'warning',
            'high' => 'danger',
            'critical' => 'dark',
        ];

        return $severityClasses[$this->severity] ?? 'secondary';
    }

    // Methods
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }
}
