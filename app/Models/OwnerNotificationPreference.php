<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OwnerNotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'owner_id',
        'business_id',
        'notification_type',
        'email_enabled',
        'sms_enabled',
        'push_enabled',
        'in_app_enabled',
        'sound_enabled',
        'priority_filter',
        'quiet_hours_start',
        'quiet_hours_end',
        'weekend_notifications',
        'digest_frequency',
        'auto_mark_read',
        'settings'
    ];

    protected $casts = [
        'email_enabled' => 'boolean',
        'sms_enabled' => 'boolean',
        'push_enabled' => 'boolean',
        'in_app_enabled' => 'boolean',
        'sound_enabled' => 'boolean',
        'weekend_notifications' => 'boolean',
        'auto_mark_read' => 'boolean',
        'settings' => 'array',
        'quiet_hours_start' => 'string',
        'quiet_hours_end' => 'string',
    ];

    // Relationships
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    // Scopes
    public function scopeForOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeForType($query, $type)
    {
        return $query->where('notification_type', $type);
    }

    // Helper methods
    public function isNotificationAllowed($priority = 'normal', $currentTime = null): bool
    {
        $currentTime = $currentTime ?: now();

        // Check priority filter
        if ($this->priority_filter && !$this->isPriorityAllowed($priority)) {
            return false;
        }

        // Check quiet hours
        if ($this->isQuietHours($currentTime)) {
            return false;
        }

        // Check weekend notifications
        if (!$this->weekend_notifications && $currentTime->isWeekend()) {
            return false;
        }

        return true;
    }

    public function isPriorityAllowed($priority): bool
    {
        if (!$this->priority_filter) {
            return true;
        }

        $priorityLevels = ['low' => 1, 'normal' => 2, 'high' => 3, 'urgent' => 4];
        $minLevel = $priorityLevels[$this->priority_filter] ?? 1;
        $currentLevel = $priorityLevels[$priority] ?? 2;

        return $currentLevel >= $minLevel;
    }

    public function isQuietHours($currentTime = null): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $currentTime = $currentTime ?: now();
        $startTime = $this->quiet_hours_start;
        $endTime = $this->quiet_hours_end;

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($startTime > $endTime) {
            return $currentTime->format('H:i') >= $startTime->format('H:i') ||
                   $currentTime->format('H:i') <= $endTime->format('H:i');
        }

        // Handle same-day quiet hours (e.g., 12:00 to 14:00)
        return $currentTime->format('H:i') >= $startTime->format('H:i') &&
               $currentTime->format('H:i') <= $endTime->format('H:i');
    }

    public function getEnabledChannels(): array
    {
        $channels = [];

        if ($this->email_enabled) $channels[] = 'email';
        if ($this->sms_enabled) $channels[] = 'sms';
        if ($this->push_enabled) $channels[] = 'push';
        if ($this->in_app_enabled) $channels[] = 'in_app';

        return $channels;
    }

    public static function getDefaultPreferences(): array
    {
        return [
            'email_enabled' => true,
            'sms_enabled' => false,
            'push_enabled' => true,
            'in_app_enabled' => true,
            'sound_enabled' => true,
            'priority_filter' => null,
            'quiet_hours_start' => null,
            'quiet_hours_end' => null,
            'weekend_notifications' => true,
            'digest_frequency' => 'daily',
            'auto_mark_read' => false,
            'settings' => []
        ];
    }

    public static function getNotificationTypes(): array
    {
        return [
            'booking' => 'Booking Notifications',
            'cancellation' => 'Cancellation Alerts',
            'payment' => 'Payment Notifications',
            'review' => 'Review Notifications',
            'system' => 'System Notifications',
            'marketing' => 'Marketing Messages',
            'alert' => 'Alert Notifications',
            'reminder' => 'Reminder Notifications',
            'customer_message' => 'Customer Messages',
            'waiting_list' => 'Waiting List Notifications'
        ];
    }

    public static function createDefaultPreferences($ownerId, $businessId): void
    {
        $types = array_keys(self::getNotificationTypes());
        $defaults = self::getDefaultPreferences();

        foreach ($types as $type) {
            self::updateOrCreate([
                'owner_id' => $ownerId,
                'business_id' => $businessId,
                'notification_type' => $type
            ], $defaults);
        }
    }

    // Global scope for enterprise isolation
    protected static function booted()
    {
        static::addGlobalScope('business_isolation', function ($builder) {
            if (auth()->check() && auth()->user()->hasRole('Business Owner')) {
                $business = auth()->user()->ownedBusinesses()->active()->first();
                if ($business) {
                    $builder->where('business_id', $business->id)
                           ->where('owner_id', auth()->id());
                }
            }
        });
    }
}
