<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'transaction_id',
        'booking_id',
        'customer_id',
        'service_id',
        'gateway_account_id',
        'gateway_type',
        'gateway_transaction_id',
        'gateway_payment_intent_id',
        'transaction_type',
        'payment_method',
        'gross_amount',
        'gateway_fees',
        'platform_fees',
        'net_amount',
        'tax_amount',
        'currency',
        'status',
        'description',
        'metadata',
        'gateway_response',
        'processed_at',
        'gateway_created_at',
        'failure_reason',
    ];

    protected $casts = [
        'gross_amount' => 'decimal:2',
        'gateway_fees' => 'decimal:2',
        'platform_fees' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'metadata' => 'array',
        'gateway_response' => 'array',
        'processed_at' => 'datetime',
        'gateway_created_at' => 'datetime',
    ];

    protected $dates = [
        'processed_at',
        'gateway_created_at',
    ];

    // Auto-generate transaction ID
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = 'TXN' . date('Ymd') . strtoupper(Str::random(8));
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function gatewayAccount(): BelongsTo
    {
        return $this->belongsTo(PaymentGatewayAccount::class, 'gateway_account_id');
    }

    public function revenueRecord(): BelongsTo
    {
        return $this->belongsTo(RevenueRecord::class, 'id', 'transaction_id');
    }

    // Scopes
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByGateway($query, $gateway)
    {
        return $query->where('gateway_type', $gateway);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('processed_at', [$startDate, $endDate]);
    }

    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    // Accessors & Mutators
    public function getFormattedAmountAttribute()
    {
        return number_format($this->gross_amount, 2);
    }

    public function getFormattedNetAmountAttribute()
    {
        return number_format($this->net_amount, 2);
    }

    public function getStatusBadgeAttribute()
    {
        $statusClasses = [
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            'refunded' => 'dark',
            'partially_refunded' => 'warning',
        ];

        return $statusClasses[$this->status] ?? 'secondary';
    }

    public function getTotalFeesAttribute()
    {
        return $this->gateway_fees + $this->platform_fees;
    }

    // Helper methods
    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    public function canBeRefunded(): bool
    {
        return $this->isSuccessful() &&
               !in_array($this->status, ['refunded', 'partially_refunded']) &&
               $this->processed_at->diffInDays(now()) <= 180; // 180 days refund window
    }

    public function isRefund(): bool
    {
        return in_array($this->transaction_type, ['refund', 'partial_refund']);
    }

    public function calculateNetAmount(): float
    {
        return $this->gross_amount - $this->gateway_fees - $this->platform_fees - $this->tax_amount;
    }

    // Static methods for analytics
    public static function getTotalRevenue($businessId, $dateRange = null)
    {
        $query = static::where('business_id', $businessId)
                      ->where('status', 'completed')
                      ->where('transaction_type', 'payment');

        if ($dateRange) {
            $query->whereBetween('processed_at', $dateRange);
        }

        return $query->sum('net_amount');
    }

    public static function getTransactionStats($businessId, $dateRange = null)
    {
        $query = static::where('business_id', $businessId);

        if ($dateRange) {
            $query->whereBetween('processed_at', $dateRange);
        }

        return [
            'total_transactions' => $query->count(),
            'successful_transactions' => $query->where('status', 'completed')->count(),
            'failed_transactions' => $query->where('status', 'failed')->count(),
            'total_volume' => $query->where('status', 'completed')->sum('gross_amount'),
            'total_fees' => $query->where('status', 'completed')->sum('gateway_fees'),
            'net_revenue' => $query->where('status', 'completed')->sum('net_amount'),
        ];
    }
}
