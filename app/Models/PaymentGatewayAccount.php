<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentGatewayAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'gateway_type',
        'account_id',
        'account_status',
        'account_details',
        'capabilities',
        'settings',
        'fee_percentage',
        'fee_fixed',
        'is_active',
        'is_primary',
        'connected_at',
        'last_sync_at',
        'webhook_endpoint',
        'webhook_secret',
    ];

    protected $casts = [
        'account_details' => 'array',
        'capabilities' => 'array',
        'settings' => 'array',
        'fee_percentage' => 'decimal:4',
        'fee_fixed' => 'decimal:2',
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
        'connected_at' => 'datetime',
        'last_sync_at' => 'datetime',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'gateway_account_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeByGateway($query, $gatewayType)
    {
        return $query->where('gateway_type', $gatewayType);
    }

    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statusClasses = [
            'pending' => 'warning',
            'active' => 'success',
            'restricted' => 'danger',
            'inactive' => 'secondary',
        ];

        return $statusClasses[$this->account_status] ?? 'secondary';
    }

    public function getFormattedFeeAttribute()
    {
        $fee = [];
        if ($this->fee_percentage) {
            $fee[] = $this->fee_percentage . '%';
        }
        if ($this->fee_fixed) {
            $fee[] = '$' . number_format($this->fee_fixed, 2);
        }
        return implode(' + ', $fee);
    }

    // Helper methods
    public function isConnected(): bool
    {
        return !empty($this->account_id) && $this->account_status === 'active';
    }

    public function canProcessPayments(): bool
    {
        return $this->isConnected() && $this->is_active;
    }

    public function calculateFees($amount): array
    {
        $percentageFee = $amount * ($this->fee_percentage / 100);
        $fixedFee = $this->fee_fixed ?? 0;
        $totalFee = $percentageFee + $fixedFee;

        return [
            'percentage_fee' => round($percentageFee, 2),
            'fixed_fee' => $fixedFee,
            'total_fee' => round($totalFee, 2),
            'net_amount' => round($amount - $totalFee, 2),
        ];
    }

    // Static methods
    public static function getPrimaryGateway($businessId)
    {
        return static::where('business_id', $businessId)
                    ->primary()
                    ->active()
                    ->first();
    }

    public static function getActiveGateways($businessId)
    {
        return static::where('business_id', $businessId)
                    ->active()
                    ->get();
    }
}
