<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PricingExperiment extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'service_id',
        'experiment_name',
        'control_price',
        'variant_price',
        'start_date',
        'end_date',
        'sample_size',
        'control_conversions',
        'variant_conversions',
        'control_views',
        'variant_views',
        'conversion_rate_control',
        'conversion_rate_variant',
        'revenue_impact',
        'statistical_significance',
        'status',
        'results_data',
    ];

    protected $casts = [
        'control_price' => 'decimal:2',
        'variant_price' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'conversion_rate_control' => 'decimal:4',
        'conversion_rate_variant' => 'decimal:4',
        'revenue_impact' => 'decimal:2',
        'statistical_significance' => 'decimal:4',
        'results_data' => 'array',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    // Scopes
    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
