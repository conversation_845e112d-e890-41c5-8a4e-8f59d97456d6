<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

// Test if the relationship exists
echo "Testing revenueRecords relationship...\n";

try {
    // Check if method exists
    $serviceClass = new ReflectionClass(\App\Models\Service::class);
    $hasMethod = $serviceClass->hasMethod('revenueRecords');

    echo "Method 'revenueRecords' exists: " . ($hasMethod ? "YES" : "NO") . "\n";

    if ($hasMethod) {
        echo "Relationship fix applied successfully!\n";
        echo "The error 'Call to undefined relationship [revenueRecords] on model [App\Models\Service]' should now be resolved.\n";
    } else {
        echo "ERROR: Method still missing!\n";
    }

} catch (Exception $e) {
    echo "Error during test: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
