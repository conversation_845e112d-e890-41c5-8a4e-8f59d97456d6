# Stripe Connect Standard Integration Setup

## Overview
This document provides instructions for setting up Stripe Connect Standard integration in the bookkei financial management system.

## Environment Variables

Add the following environment variables to your `.env` file:

```env
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_CLIENT_ID=ca_your_client_id_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PLATFORM_FEE_RATE=0.025
```

## Stripe Dashboard Setup

### 1. Create Stripe Account
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Create an account or log in to existing account
3. Complete account verification

### 2. Enable Connect
1. In Stripe Dashboard, go to **Connect** → **Settings**
2. Enable **Express accounts** for your platform
3. Configure your platform settings:
   - Platform name: "Bookkei"
   - Platform website: Your domain
   - Support email: Your support email

### 3. Get API Keys
1. Go to **Developers** → **API keys**
2. Copy your **Publishable key** and **Secret key**
3. For Connect, go to **Connect** → **Settings** → **Integration**
4. Copy your **Client ID**

### 4. Setup Webhooks
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://yourdomain.com/webhooks/stripe`
4. Select events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `payment_intent.requires_action`
   - `charge.dispute.created`
   - `account.updated`
   - `payout.paid`
   - `payout.failed`
5. Copy the **Signing secret**

## Installation Steps

### 1. Install Dependencies
```bash
composer install
```

### 2. Run Migrations
```bash
php artisan migrate
```

### 3. Clear Cache
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
```

## Testing

### Test Cards
Use these test card numbers in development:

**Successful Payments:**
- Visa: `****************`
- Visa (debit): `****************`
- Mastercard: `****************`
- American Express: `***************`

**Failed Payments:**
- Generic decline: `****************`
- Insufficient funds: `****************`
- Lost card: `****************`

**3D Secure:**
- Authentication required: `****************`
- Authentication fails: `****************`

### Test Flow
1. Connect a test Stripe account through `/owner/payments/gateways`
2. Process a test payment using the API endpoint
3. Verify webhook events are received
4. Check transaction records in database

## API Usage

### Process Payment
```javascript
// Frontend JavaScript
fetch('/owner/payments/stripe/process', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        payment_method_id: 'pm_card_visa',
        amount: 50.00,
        currency: 'USD',
        booking_id: 123,
        customer_id: 456,
        service_id: 789
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        if (data.requires_action) {
            // Handle 3D Secure authentication
            stripe.confirmCardPayment(data.client_secret);
        } else {
            // Payment succeeded
            console.log('Payment completed:', data.transaction);
        }
    } else {
        console.error('Payment failed:', data.message);
    }
});
```

### Refund Payment
```php
// In your controller
$transaction = PaymentTransaction::find($transactionId);
$result = $this->stripeService->processRefund($transaction, $refundAmount);

if ($result['success']) {
    // Refund processed successfully
    return response()->json(['success' => true]);
} else {
    return response()->json(['error' => $result['error']], 400);
}
```

## Security Considerations

### 1. Webhook Security
- Always verify webhook signatures using the webhook secret
- Use HTTPS for all webhook endpoints
- Implement idempotency for webhook processing

### 2. API Key Security
- Never expose secret keys in frontend code
- Use environment variables for all sensitive data
- Rotate keys regularly

### 3. Business Isolation
- Each business has its own Stripe Connect account
- Financial data is completely isolated between businesses
- Middleware enforces business-level access control

## Monitoring

### 1. Logs
Monitor these log channels:
- `stripe_connect.log` - Stripe API interactions
- `webhooks.log` - Webhook processing
- `payments.log` - Payment processing

### 2. Metrics
Track these key metrics:
- Payment success rate
- Average processing time
- Webhook delivery success
- Account connection status

## Troubleshooting

### Common Issues

**1. Webhook Not Receiving Events**
- Check webhook URL is accessible
- Verify webhook secret is correct
- Check firewall settings

**2. Account Connection Fails**
- Verify client ID is correct
- Check redirect URLs are whitelisted
- Ensure business information is complete

**3. Payment Processing Errors**
- Check API keys are valid
- Verify account has charges enabled
- Check payment method is valid

### Debug Mode
Enable debug logging in `.env`:
```env
LOG_LEVEL=debug
STRIPE_DEBUG=true
```

## Production Deployment

### 1. Switch to Live Keys
Replace test keys with live keys in production:
```env
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
```

### 2. Update Webhook URL
Update webhook endpoint to production URL in Stripe Dashboard

### 3. Enable SSL
Ensure SSL certificate is properly configured for webhook security

### 4. Monitor Performance
Set up monitoring for:
- Payment processing latency
- Webhook delivery success
- Error rates
- Account health

## Support

For technical support:
- Check Stripe documentation: https://stripe.com/docs/connect
- Review webhook logs for debugging
- Contact Stripe support for account-specific issues

For platform support:
- Check application logs
- Review database transaction records
- Verify business isolation is working correctly
