# Stripe Connect Standard Accounts - Testing Guide

## Quick Test Checklist

### ✅ Prerequisites
- [ ] Stripe test keys configured in `.env`
- [ ] Stripe Connect Client ID configured
- [ ] Webhook endpoint configured in Stripe Dashboard
- [ ] Database migrations run
- [ ] Application running locally

### ✅ Environment Setup

```env
# Required Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CLIENT_ID=ca_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PLATFORM_FEE_RATE=0.025
STRIPE_DEFAULT_COUNTRY=US
```

### ✅ Test Standard Account Creation

1. **Navigate to Payment Gateways**
   ```
   http://localhost:8000/owner/payments/gateways
   ```

2. **Verify Standard Account Display**
   - Should show "Stripe Connect Standard" title
   - Should mention "Direct Stripe Dashboard access"
   - Should show "Full account control" feature

3. **Click "Connect with Stripe"**
   - Should redirect to Stripe onboarding
   - URL should contain `connect.stripe.com`
   - Should show your platform branding

### ✅ Test Onboarding Flow

1. **Complete Stripe Onboarding**
   - Use test business information
   - Complete all required fields
   - Accept terms and conditions

2. **Verify Return Handling**
   - Should redirect back to `/owner/payments/gateways`
   - Should show success message
   - Account status should update

3. **Check Database Records**
   ```sql
   SELECT * FROM payment_gateway_accounts 
   WHERE gateway_type = 'stripe' 
   ORDER BY created_at DESC LIMIT 1;
   ```

### ✅ Test Account Management

1. **Sync Account**
   - Click "Sync Account" button
   - Should update account status
   - Should show current capabilities

2. **Disconnect Account**
   - Click "Disconnect" button
   - Should show confirmation dialog
   - Should mark account as inactive

3. **Reconnect Account**
   - Should allow reconnection
   - Should reuse existing Stripe account

### ✅ Test Account States

#### Pending State
- `details_submitted: false`
- `charges_enabled: false`
- Should show "Complete Setup" button

#### Active State
- `details_submitted: true`
- `charges_enabled: true`
- Should show "Connected" badge

#### Incomplete State
- `details_submitted: true`
- `charges_enabled: false`
- Should show additional requirements

### ✅ Test Webhook Processing

1. **Install Stripe CLI**
   ```bash
   stripe listen --forward-to localhost:8000/webhooks/stripe
   ```

2. **Trigger Test Events**
   ```bash
   stripe trigger account.updated
   stripe trigger payment_intent.succeeded
   ```

3. **Verify Event Processing**
   - Check application logs
   - Verify database updates
   - Confirm account status sync

### ✅ Test Payment Processing

1. **Create Test Payment**
   ```php
   $stripeService = app(\App\Services\StripeConnectService::class);
   
   $result = $stripeService->processPayment([
       'business_id' => 1,
       'amount' => 2000, // $20.00
       'currency' => 'usd',
       'payment_method_id' => 'pm_card_visa',
       'customer_id' => 1,
       'booking_id' => 1,
       'service_id' => 1
   ]);
   ```

2. **Verify Payment Records**
   - Check `payment_transactions` table
   - Verify fee calculations
   - Confirm revenue records

### ✅ Test Error Handling

1. **Invalid Account**
   - Try connecting with invalid credentials
   - Should show appropriate error message

2. **Expired Link**
   - Let account link expire
   - Should redirect to refresh URL
   - Should regenerate new link

3. **Webhook Failures**
   - Test with invalid signature
   - Should log error and return 400

### ✅ Verification Commands

```bash
# Check service configuration
php artisan tinker
config('services.stripe')

# Test Stripe connection
$stripe = app(\App\Services\StripeConnectService::class);

# Check account creation
$result = $stripe->createAccountLink(1, 1);
dd($result);

# Verify webhook endpoint
curl -X POST http://localhost:8000/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

### ✅ Production Readiness

1. **Switch to Live Keys**
   - Update environment variables
   - Configure live webhook endpoints
   - Test with real bank account

2. **Security Verification**
   - Verify HTTPS in production
   - Check webhook signature validation
   - Confirm data isolation

3. **Monitoring Setup**
   - Configure error logging
   - Set up payment success rate monitoring
   - Enable Stripe Dashboard alerts

## Common Issues & Solutions

### Issue: "Connect with Stripe" button not working
**Solution**: Check Stripe Client ID configuration

### Issue: Webhook events not received
**Solution**: Verify webhook URL and signature secret

### Issue: Account creation fails
**Solution**: Check Stripe API keys and permissions

### Issue: Payment processing errors
**Solution**: Verify connected account is active and has required capabilities

## Success Criteria

- ✅ Standard accounts created with `type: 'standard'`
- ✅ Proper onboarding flow with return/refresh URLs
- ✅ Account status tracking and synchronization
- ✅ Webhook events processed correctly
- ✅ Payment processing works end-to-end
- ✅ Error handling provides clear feedback
- ✅ Database records maintain data integrity
- ✅ Enterprise isolation enforced

## Next Steps

After successful testing:
1. Configure production Stripe keys
2. Set up live webhook endpoints
3. Test with real payment methods
4. Monitor payment success rates
5. Implement additional features as needed

## Support Resources

- [Stripe Connect Standard Documentation](https://docs.stripe.com/connect/standard-accounts)
- [Stripe Testing Guide](https://docs.stripe.com/testing)
- [Webhook Testing](https://docs.stripe.com/webhooks/test)
- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
