# Stripe Connect Standard Accounts Implementation

## Overview

This implementation follows the official [Stripe Connect Standard Accounts documentation](https://docs.stripe.com/connect/standard-accounts) to provide a complete integration with Stripe Connect using Standard connected accounts.

## Key Features

### ✅ Standard Account Type
- Uses `type: 'standard'` when creating accounts
- Provides full Stripe Dashboard access to connected accounts
- Direct relationship between connected accounts and Stripe

### ✅ Connect Onboarding Flow
- Implements the official Connect Onboarding process
- Uses Account Links API for seamless user experience
- Proper handling of `return_url` and `refresh_url`

### ✅ Account Management
- Account creation with proper metadata
- Status tracking (`details_submitted`, `charges_enabled`)
- Capability management (`card_payments`, `transfers`)
- Real-time account synchronization

## Implementation Details

### Account Creation Process

1. **Create Standard Account**
   ```php
   $account = Account::create([
       'type' => 'standard',
       'country' => config('services.stripe.default_country', 'US'),
       'email' => auth()->user()->email,
       'capabilities' => [
           'card_payments' => ['requested' => true],
           'transfers' => ['requested' => true],
       ],
       'business_type' => 'individual',
       'metadata' => [
           'business_id' => $businessId,
           'owner_id' => $ownerId,
           'platform' => 'bookkei'
       ]
   ]);
   ```

2. **Create Account Link**
   ```php
   $accountLink = AccountLink::create([
       'account' => $stripeAccountId,
       'refresh_url' => route('owner.payments.stripe.refresh'),
       'return_url' => route('owner.payments.stripe.return'),
       'type' => 'account_onboarding',
   ]);
   ```

3. **Redirect to Onboarding**
   - User is redirected to `$accountLink->url`
   - Stripe handles the onboarding process
   - User returns to platform after completion

### URL Handling

#### Return URL
- Triggered when user completes onboarding flow
- Checks `details_submitted` and `charges_enabled` status
- Updates local account records

#### Refresh URL
- Triggered when link expires or needs regeneration
- Creates new account link automatically
- Provides seamless user experience

### Account Status Management

The system tracks multiple account states:

- **details_submitted**: Whether user completed onboarding
- **charges_enabled**: Whether account can process payments
- **payouts_enabled**: Whether account can receive payouts
- **account_status**: Local status (pending/active/disconnected)

### Security Features

#### Enterprise Isolation
- Each business has separate Stripe accounts
- Complete financial data separation
- Business-specific webhook processing

#### Webhook Security
- Signature verification using webhook secret
- Idempotent event processing
- Comprehensive error handling

### Supported Webhook Events

- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `payment_intent.requires_action`
- `charge.dispute.created`
- `account.updated`
- `payout.paid`
- `payout.failed`

## Configuration

### Environment Variables

```env
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CLIENT_ID=ca_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PLATFORM_FEE_RATE=0.025
STRIPE_DEFAULT_COUNTRY=US
```

### Service Configuration

```php
'stripe' => [
    'key' => env('STRIPE_PUBLISHABLE_KEY'),
    'secret' => env('STRIPE_SECRET_KEY'),
    'client_id' => env('STRIPE_CLIENT_ID'),
    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
    'platform_fee_rate' => env('STRIPE_PLATFORM_FEE_RATE', 0.025),
    'default_country' => env('STRIPE_DEFAULT_COUNTRY', 'US'),
],
```

## Routes

```php
// Stripe Connect Routes
Route::post('/stripe/connect', 'connectStripe')->name('stripe.connect');
Route::get('/stripe/return', 'stripeReturn')->name('stripe.return');
Route::get('/stripe/refresh', 'stripeRefresh')->name('stripe.refresh');
Route::post('/stripe/disconnect', 'disconnectStripe')->name('stripe.disconnect');
Route::post('/stripe/sync', 'syncStripeAccount')->name('stripe.sync');
Route::post('/stripe/process', 'processStripePayment')->name('stripe.process');

// Webhook Route
Route::post('/webhooks/stripe', 'StripeWebhookController@handleWebhook');
```

## User Interface

### Gateway Management Page
- Clear status indicators
- Action buttons for connect/disconnect/sync
- Feature highlights specific to Standard accounts
- Proper error handling and user feedback

### Standard Account Benefits
- Direct Stripe Dashboard access
- Full account control
- Complete payment processing capabilities
- Real-time fraud protection
- Multiple payment methods support

## Compliance

### PCI Compliance
- Stripe handles all sensitive card data
- No card data stored in application
- Secure tokenization for payments

### Data Protection
- Enterprise-level data isolation
- Audit logging for all operations
- Secure webhook processing

## Testing

### Test Mode
- Use Stripe test keys for development
- Test with Stripe test cards
- Verify webhook delivery

### Production Checklist
- Configure production keys
- Set up real webhooks
- Test with real cards (small amounts)
- Monitor logs and success rates

## Support

For issues or questions:
1. Check Stripe Dashboard for account status
2. Review application logs
3. Verify webhook configuration
4. Test with Stripe CLI for webhook debugging

## Documentation References

- [Stripe Connect Standard Accounts](https://docs.stripe.com/connect/standard-accounts)
- [Account Links API](https://docs.stripe.com/api/account_links)
- [Connect Webhooks](https://docs.stripe.com/connect/webhooks)
- [Testing Connect](https://docs.stripe.com/connect/testing)
