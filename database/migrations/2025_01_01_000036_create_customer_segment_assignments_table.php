<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_segment_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('segment_id')->constrained('customer_segments')->onDelete('cascade');
            $table->timestamp('assigned_at');
            $table->boolean('is_automatic')->default(true);
            
            $table->timestamps();
            
            // Unique constraint to prevent duplicate assignments
            $table->unique(['business_id', 'customer_id', 'segment_id'], 'cust_seg_assign_unique');
            
            // Indexes
            $table->index(['business_id', 'segment_id']);
            $table->index(['business_id', 'customer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_segment_assignments');
    }
};
