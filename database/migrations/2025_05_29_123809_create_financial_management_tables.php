<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Business financial configuration
        Schema::create('business_financial_config', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->string('default_currency', 3)->default('USD');
            $table->decimal('tax_rate', 5, 4)->nullable();
            $table->decimal('platform_fee_rate', 5, 4)->default(0.0000);
            $table->json('revenue_goals')->nullable(); // Monthly/yearly revenue goals
            $table->enum('pricing_strategy_default', ['fixed', 'dynamic', 'seasonal'])->default('fixed');
            $table->json('payment_gateway_preferences')->nullable();
            $table->date('financial_year_start')->nullable();
            $table->enum('accounting_method', ['cash', 'accrual'])->default('cash');
            $table->timestamps();

            $table->unique('business_id');
            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['business_id', 'owner_id']);
        });

        // Payment gateway accounts for dual gateway integration
        Schema::create('payment_gateway_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->enum('gateway_type', ['stripe', 'paypal']);
            $table->string('account_id')->nullable(); // Stripe Connect account ID or PayPal merchant ID
            $table->string('account_status')->default('pending'); // pending, active, restricted, inactive
            $table->json('account_details')->nullable(); // Gateway-specific account information
            $table->json('capabilities')->nullable(); // Available payment capabilities
            $table->json('settings')->nullable(); // Gateway-specific settings
            $table->decimal('fee_percentage', 5, 4)->nullable(); // Gateway fee percentage
            $table->decimal('fee_fixed', 8, 2)->nullable(); // Fixed fee per transaction
            $table->boolean('is_active')->default(false);
            $table->boolean('is_primary')->default(false); // Primary gateway for the business
            $table->timestamp('connected_at')->nullable();
            $table->timestamp('last_sync_at')->nullable();
            $table->text('webhook_endpoint')->nullable();
            $table->string('webhook_secret')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['business_id', 'gateway_type']);
            $table->index(['business_id', 'is_active']);
            $table->unique(['business_id', 'gateway_type']); // One account per gateway per business
        });

        // Payment transactions for comprehensive payment processing
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->string('transaction_id')->unique(); // Internal transaction ID
            $table->unsignedBigInteger('booking_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('service_id')->nullable();
            $table->unsignedBigInteger('gateway_account_id');
            $table->enum('gateway_type', ['stripe', 'paypal', 'manual']);
            $table->string('gateway_transaction_id')->nullable(); // External gateway transaction ID
            $table->string('gateway_payment_intent_id')->nullable(); // Payment intent ID for Stripe
            $table->enum('transaction_type', ['payment', 'refund', 'partial_refund', 'chargeback', 'dispute']);
            $table->enum('payment_method', ['card', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay', 'cash', 'other']);
            $table->decimal('gross_amount', 10, 2);
            $table->decimal('gateway_fees', 8, 2)->default(0);
            $table->decimal('platform_fees', 8, 2)->default(0);
            $table->decimal('net_amount', 10, 2);
            $table->decimal('tax_amount', 8, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded']);
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional transaction data
            $table->json('gateway_response')->nullable(); // Raw gateway response
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('gateway_created_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('booking_id')->references('id')->on('bookings')->onDelete('set null');
            $table->foreign('gateway_account_id')->references('id')->on('payment_gateway_accounts')->onDelete('cascade');
            $table->index(['business_id', 'status']);
            $table->index(['business_id', 'gateway_type']);
            $table->index(['business_id', 'processed_at']);
            $table->index(['customer_id', 'business_id']);
            $table->index(['service_id', 'business_id']);
        });

        // Revenue records for comprehensive revenue analytics
        Schema::create('revenue_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->unsignedBigInteger('transaction_id')->nullable();
            $table->enum('gateway_type', ['stripe', 'paypal', 'manual']);
            $table->unsignedBigInteger('service_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('booking_id')->nullable();
            $table->decimal('gross_amount', 10, 2);
            $table->decimal('gateway_fees', 8, 2)->default(0);
            $table->decimal('platform_fees', 8, 2)->default(0);
            $table->decimal('net_amount', 10, 2);
            $table->decimal('tax_amount', 8, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->date('revenue_date');
            $table->enum('revenue_category', ['service', 'product', 'addon', 'tip', 'package', 'subscription']);
            $table->string('payment_method', 50)->nullable();
            $table->decimal('refund_amount', 8, 2)->default(0);
            $table->enum('status', ['pending', 'completed', 'refunded', 'disputed']);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('transaction_id')->references('id')->on('payment_transactions')->onDelete('set null');
            $table->foreign('service_id')->references('id')->on('services')->onDelete('set null');
            $table->foreign('customer_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('booking_id')->references('id')->on('bookings')->onDelete('set null');
            $table->index(['business_id', 'revenue_date']);
            $table->index(['service_id', 'revenue_date']);
            $table->index(['customer_id', 'revenue_date']);
            $table->index(['business_id', 'status']);
        });

        // Pricing strategies for dynamic pricing management
        Schema::create('pricing_strategies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->unsignedBigInteger('service_id')->nullable();
            $table->string('strategy_name');
            $table->enum('strategy_type', ['fixed', 'dynamic', 'seasonal', 'demand_based', 'package', 'tier_based']);
            $table->decimal('base_price', 10, 2);
            $table->decimal('minimum_price', 10, 2)->nullable();
            $table->decimal('maximum_price', 10, 2)->nullable();
            $table->json('pricing_rules')->nullable(); // Dynamic pricing rules
            $table->json('seasonal_adjustments')->nullable();
            $table->json('discount_rules')->nullable();
            $table->json('package_pricing')->nullable();
            $table->json('tier_pricing')->nullable(); // Different pricing tiers
            $table->date('effective_from')->nullable();
            $table->date('effective_to')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
            $table->index(['business_id', 'service_id']);
            $table->index(['effective_from', 'effective_to']);
            $table->index(['business_id', 'is_active']);
        });

        // Pricing experiments for A/B testing
        Schema::create('pricing_experiments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->unsignedBigInteger('service_id');
            $table->string('experiment_name');
            $table->decimal('control_price', 10, 2);
            $table->decimal('variant_price', 10, 2);
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('sample_size')->default(0);
            $table->integer('control_conversions')->default(0);
            $table->integer('variant_conversions')->default(0);
            $table->integer('control_views')->default(0);
            $table->integer('variant_views')->default(0);
            $table->decimal('conversion_rate_control', 5, 4)->default(0);
            $table->decimal('conversion_rate_variant', 5, 4)->default(0);
            $table->decimal('revenue_impact', 10, 2)->default(0);
            $table->decimal('statistical_significance', 5, 4)->default(0);
            $table->enum('status', ['draft', 'running', 'completed', 'paused']);
            $table->json('results_data')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
            $table->index(['business_id', 'status']);
            $table->index(['service_id', 'status']);
            $table->index(['start_date', 'end_date']);
        });

        // Financial performance metrics
        Schema::create('financial_metrics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->date('metric_date');
            $table->decimal('total_revenue', 12, 2)->default(0);
            $table->integer('total_transactions')->default(0);
            $table->decimal('average_transaction_value', 10, 2)->default(0);
            $table->decimal('gateway_fees_total', 10, 2)->default(0);
            $table->decimal('platform_fees_total', 8, 2)->default(0);
            $table->decimal('net_revenue', 12, 2)->default(0);
            $table->integer('customer_count')->default(0);
            $table->integer('new_customer_count')->default(0);
            $table->integer('returning_customer_count')->default(0);
            $table->decimal('refund_amount', 10, 2)->default(0);
            $table->decimal('chargeback_amount', 10, 2)->default(0);
            $table->decimal('revenue_growth_rate', 5, 4)->default(0); // Period over period
            $table->json('payment_method_breakdown')->nullable();
            $table->json('service_revenue_breakdown')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->index(['business_id', 'metric_date']);
            $table->unique(['business_id', 'metric_date']);
        });

        // Payment method configurations
        Schema::create('payment_method_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->string('method_type'); // card, bank_transfer, paypal, etc.
            $table->string('method_name');
            $table->boolean('is_enabled')->default(true);
            $table->json('configuration')->nullable(); // Method-specific configuration
            $table->decimal('processing_fee', 5, 4)->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['business_id', 'is_enabled']);
        });

        // Financial alerts and notifications
        Schema::create('financial_alerts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('owner_id');
            $table->enum('alert_type', ['revenue_goal', 'payment_failure', 'chargeback', 'unusual_activity', 'gateway_issue']);
            $table->string('title');
            $table->text('message');
            $table->enum('severity', ['low', 'medium', 'high', 'critical']);
            $table->json('alert_data')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamp('triggered_at');
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['business_id', 'is_read']);
            $table->index(['business_id', 'alert_type']);
            $table->index(['business_id', 'severity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_alerts');
        Schema::dropIfExists('payment_method_configs');
        Schema::dropIfExists('financial_metrics');
        Schema::dropIfExists('pricing_experiments');
        Schema::dropIfExists('pricing_strategies');
        Schema::dropIfExists('revenue_records');
        Schema::dropIfExists('payment_transactions');
        Schema::dropIfExists('payment_gateway_accounts');
        Schema::dropIfExists('business_financial_config');
    }
};
