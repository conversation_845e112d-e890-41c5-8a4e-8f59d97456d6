# Stripe Connect Integration Guide for Bookkei

## 🚀 Quick Setup Guide

### Step 1: Configure Environment Variables

Add these variables to your `.env` file:

```env
# Stripe Configuration (Test Mode)
STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef...
STRIPE_SECRET_KEY=sk_test_51234567890abcdef...
STRIPE_CLIENT_ID=ca_1234567890abcdef...
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
STRIPE_PLATFORM_FEE_RATE=0.025
```

### Step 2: Get Your Stripe Keys

#### 2.1 Create/Login to Stripe Account
1. Go to [https://dashboard.stripe.com](https://dashboard.stripe.com)
2. Create account or login
3. Complete account verification

#### 2.2 Get API Keys
1. In Stripe Dashboard → **Developers** → **API keys**
2. Copy **Publishable key** (starts with `pk_test_`)
3. Copy **Secret key** (starts with `sk_test_`)

#### 2.3 Enable Stripe Connect
1. Go to **Connect** → **Settings**
2. Click **Get started** if not already enabled
3. Fill in platform details:
   - **Platform name**: Bookkei
   - **Platform website**: Your domain
   - **Support email**: Your support email

#### 2.4 Get Connect Client ID
1. In **Connect** → **Settings** → **Integration**
2. Copy **Client ID** (starts with `ca_`)

#### 2.5 Setup Webhook Endpoint
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. **Endpoint URL**: `https://yourdomain.com/webhooks/stripe`
4. **Events to send**:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `payment_intent.requires_action`
   - `charge.dispute.created`
   - `account.updated`
   - `payout.paid`
   - `payout.failed`
5. Copy **Signing secret** (starts with `whsec_`)

### Step 3: Update Configuration

After adding environment variables, run:

```bash
php artisan config:clear
php artisan cache:clear
```

### Step 4: Connect Stripe Account

1. Navigate to `/owner/payments/gateways` in your application
2. Click **"Connect with Stripe"** button
3. You'll be redirected to Stripe's onboarding flow
4. Complete the account setup process
5. You'll be redirected back to your application

## 🔧 Technical Implementation Details

### Current Integration Status ✅

The following components are already implemented:

#### ✅ Backend Services
- **StripeConnectService**: Complete payment processing service
- **PaymentController**: Full Stripe integration methods
- **WebhookController**: Secure webhook event handling
- **Database Models**: PaymentGatewayAccount, PaymentTransaction, RevenueRecord

#### ✅ Routes Configuration
```php
// Stripe Connect Routes
Route::post('/stripe/connect', 'connectStripe')->name('stripe.connect');
Route::get('/stripe/return', 'stripeReturn')->name('stripe.return');
Route::get('/stripe/refresh', 'stripeRefresh')->name('stripe.refresh');
Route::post('/stripe/disconnect', 'disconnectStripe')->name('stripe.disconnect');
Route::post('/stripe/sync', 'syncStripeAccount')->name('stripe.sync');
Route::post('/stripe/process', 'processStripePayment')->name('stripe.process');

// Webhook Route
Route::post('/webhooks/stripe', 'StripeWebhookController@handleWebhook');
```

#### ✅ Frontend Interface
- **Gateway Management UI**: Connect/disconnect/sync buttons
- **Status Display**: Account status, capabilities, fees
- **AJAX Integration**: Real-time account management

### Connection Flow

1. **User clicks "Connect with Stripe"**
   - `POST /owner/payments/stripe/connect`
   - Creates Stripe Express account
   - Generates onboarding link
   - Redirects to Stripe

2. **Stripe Onboarding**
   - User completes business information
   - Stripe verifies account details
   - Account capabilities are enabled

3. **Return to Application**
   - `GET /owner/payments/stripe/return?account=acct_xxx`
   - Updates account status in database
   - Shows success/pending message

4. **Webhook Processing**
   - Real-time account updates via webhooks
   - Payment event processing
   - Revenue record creation

### Database Schema

The integration uses these tables:

```sql
-- Gateway account management
payment_gateway_accounts (
    id, business_id, owner_id, gateway_type, account_id,
    account_status, account_details, capabilities, settings,
    fee_percentage, fee_fixed, is_active, is_primary,
    connected_at, last_sync_at, webhook_endpoint, webhook_secret
)

-- Transaction processing
payment_transactions (
    id, business_id, owner_id, transaction_id, booking_id,
    customer_id, service_id, gateway_account_id, gateway_type,
    gateway_transaction_id, gateway_payment_intent_id,
    transaction_type, payment_method, gross_amount, gateway_fees,
    platform_fees, net_amount, tax_amount, currency, status,
    description, metadata, gateway_response, processed_at,
    gateway_created_at, failure_reason
)

-- Revenue tracking
revenue_records (
    id, business_id, owner_id, transaction_id, gateway_type,
    service_id, customer_id, booking_id, gross_amount,
    gateway_fees, platform_fees, net_amount, tax_amount,
    currency, revenue_date, revenue_category, payment_method,
    refund_amount, status, metadata
)
```

## 🧪 Testing

### Test with Stripe Test Cards

```javascript
// Test card numbers
const testCards = {
    visa: '****************',
    visaDebit: '****************',
    mastercard: '****************',
    amex: '***************',
    declined: '****************',
    insufficientFunds: '****************',
    require3DS: '****************'
};
```

### Test Payment Processing

```javascript
// Example payment processing
fetch('/owner/payments/stripe/process', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        payment_method_id: 'pm_card_visa',
        amount: 50.00,
        currency: 'USD',
        booking_id: 123
    })
})
.then(response => response.json())
.then(data => console.log('Payment result:', data));
```

## 🔒 Security Features

### ✅ Enterprise Isolation
- Each business has separate Stripe Connect accounts
- Complete financial data isolation
- Business-specific webhook processing

### ✅ Webhook Security
- Signature verification using webhook secret
- Idempotent event processing
- Comprehensive error handling

### ✅ PCI Compliance
- Stripe handles all sensitive card data
- No card data stored in application
- Secure tokenization for payments

## 📊 Features Available

### ✅ Payment Processing
- Credit/debit card payments
- Apple Pay & Google Pay
- 3D Secure authentication
- Multi-currency support

### ✅ Account Management
- Express account onboarding
- Real-time status sync
- Capability monitoring
- Fee calculation

### ✅ Revenue Integration
- Automatic revenue recording
- Fee breakdown tracking
- Multi-gateway analytics
- Real-time reporting

### ✅ Refund Processing
- Full and partial refunds
- Automatic transaction records
- Revenue adjustment
- Notification system

## 🚨 Troubleshooting

### Common Issues

**1. "Connect with Stripe" button not working**
- Check environment variables are set
- Verify Stripe keys are correct
- Check application logs for errors

**2. Webhook events not received**
- Verify webhook URL is accessible
- Check webhook secret is correct
- Ensure SSL certificate is valid

**3. Account connection fails**
- Verify Client ID is correct
- Check business information is complete
- Review Stripe account status

### Debug Commands

```bash
# Check configuration
php artisan config:show services.stripe

# Clear cache
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Check logs
tail -f storage/logs/laravel.log
```

## 🎯 Next Steps

After successful connection:

1. **Test Payment Processing**: Use test cards to verify payments work
2. **Configure Webhooks**: Test webhook delivery and processing
3. **Setup Production**: Switch to live keys when ready
4. **Monitor Performance**: Track payment success rates and errors

The Stripe Connect integration is now ready for use! 🎉
