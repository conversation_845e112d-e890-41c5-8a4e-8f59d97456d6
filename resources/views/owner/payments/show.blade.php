@extends('owner.layouts.app')

@section('title', 'Transaction Details')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-receipt mr-2"></i>Transaction Details</h1>
            <p class="text-muted mb-0">View complete transaction information and history</p>
        </div>
        <div>
            <a href="{{ route('owner.payments.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Payments
            </a>
        </div>
    </div>
@stop

@section('content')
<div class="row">
    <!-- Transaction Summary -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-2"></i>
                    Transaction Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $transaction->status_badge }} badge-lg">
                        {{ ucfirst(str_replace('_', ' ', $transaction->status)) }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Transaction Details</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Transaction ID:</strong></td>
                                <td><code>{{ $transaction->transaction_id }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Gateway Transaction ID:</strong></td>
                                <td>
                                    @if($transaction->gateway_transaction_id)
                                        <code>{{ $transaction->gateway_transaction_id }}</code>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Gateway:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $transaction->gateway_type == 'stripe' ? 'primary' : ($transaction->gateway_type == 'paypal' ? 'info' : 'secondary') }}">
                                        {{ ucfirst($transaction->gateway_type) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $transaction->payment_method)) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Transaction Type:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Currency:</strong></td>
                                <td>{{ strtoupper($transaction->currency) }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h6 class="text-muted">Timing Information</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $transaction->created_at->format('M j, Y \a\t g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Processed:</strong></td>
                                <td>
                                    @if($transaction->processed_at)
                                        {{ $transaction->processed_at->format('M j, Y \a\t g:i A') }}
                                    @else
                                        <span class="text-muted">Not processed</span>
                                    @endif
                                </td>
                            </tr>
                            @if($transaction->gateway_created_at)
                            <tr>
                                <td><strong>Gateway Created:</strong></td>
                                <td>{{ $transaction->gateway_created_at->format('M j, Y \a\t g:i A') }}</td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Processing Time:</strong></td>
                                <td>
                                    @if($transaction->processed_at && $transaction->created_at)
                                        {{ $transaction->created_at->diffForHumans($transaction->processed_at, true) }}
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($transaction->description)
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-muted">Description</h6>
                        <p class="mb-0">{{ $transaction->description }}</p>
                    </div>
                </div>
                @endif

                @if($transaction->failure_reason && $transaction->status == 'failed')
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> Failure Reason</h6>
                            <p class="mb-0">{{ $transaction->failure_reason }}</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Amount Breakdown -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calculator mr-2"></i>
                    Amount Breakdown
                </h3>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Gross Amount:</strong></td>
                        <td class="text-right">${{ number_format($transaction->gross_amount, 2) }}</td>
                    </tr>
                    @if($transaction->tax_amount > 0)
                    <tr>
                        <td>Tax Amount:</td>
                        <td class="text-right">${{ number_format($transaction->tax_amount, 2) }}</td>
                    </tr>
                    @endif
                    @if($transaction->gateway_fees > 0)
                    <tr>
                        <td>Gateway Fees:</td>
                        <td class="text-right text-muted">-${{ number_format($transaction->gateway_fees, 2) }}</td>
                    </tr>
                    @endif
                    @if($transaction->platform_fees > 0)
                    <tr>
                        <td>Platform Fees:</td>
                        <td class="text-right text-muted">-${{ number_format($transaction->platform_fees, 2) }}</td>
                    </tr>
                    @endif
                    <tr class="border-top">
                        <td><strong>Net Amount:</strong></td>
                        <td class="text-right"><strong class="text-success">${{ number_format($transaction->net_amount, 2) }}</strong></td>
                    </tr>
                </table>

                @if($transaction->canBeRefunded())
                <div class="mt-3">
                    <button class="btn btn-warning btn-block" data-toggle="modal" data-target="#refundModal">
                        <i class="fas fa-undo"></i> Process Refund
                    </button>
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tools mr-2"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($transaction->booking)
                    <a href="{{ route('owner.bookings.show', $transaction->booking_id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar"></i> View Booking
                    </a>
                    @endif

                    @if($transaction->customer)
                    <a href="{{ route('owner.customers.show', $transaction->customer_id) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-user"></i> View Customer
                    </a>
                    @endif

                    <button class="btn btn-outline-secondary btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print"></i> Print Receipt
                    </button>

                    <button class="btn btn-outline-dark btn-sm" onclick="downloadReceipt()">
                        <i class="fas fa-download"></i> Download PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer & Booking Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user mr-2"></i>
                    Customer Information
                </h3>
            </div>
            <div class="card-body">
                @if($transaction->customer)
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px;">
                                {{ strtoupper(substr($transaction->customer->name, 0, 1)) }}
                            </div>
                        </div>
                        <div>
                            <h5 class="mb-1">{{ $transaction->customer->name }}</h5>
                            <p class="text-muted mb-0">{{ $transaction->customer->email }}</p>
                            @if($transaction->customer->phone)
                                <p class="text-muted mb-0">{{ $transaction->customer->phone }}</p>
                            @endif
                        </div>
                    </div>
                @else
                    <p class="text-muted">Guest customer - no profile available</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar mr-2"></i>
                    Booking Information
                </h3>
            </div>
            <div class="card-body">
                @if($transaction->booking)
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Booking ID:</strong></td>
                            <td><code>{{ $transaction->booking->booking_number }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>Service:</strong></td>
                            <td>{{ $transaction->service->name ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Date & Time:</strong></td>
                            <td>{{ $transaction->booking->start_datetime ? $transaction->booking->start_datetime->format('M j, Y \a\t g:i A') : 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge badge-{{ $transaction->booking->status_color }}">
                                    {{ ucfirst($transaction->booking->status) }}
                                </span>
                            </td>
                        </tr>
                    </table>
                @else
                    <p class="text-muted">No booking associated with this transaction</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Related Transactions -->
@if($relatedTransactions->count() > 0)
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-2"></i>
                    Related Transactions
                </h3>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Transaction ID</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($relatedTransactions as $related)
                        <tr>
                            <td><code>{{ $related->transaction_id }}</code></td>
                            <td>{{ ucfirst(str_replace('_', ' ', $related->transaction_type)) }}</td>
                            <td>{{ $related->processed_at ? $related->processed_at->format('M j, Y H:i') : 'Pending' }}</td>
                            <td>${{ number_format($related->gross_amount, 2) }}</td>
                            <td>
                                <span class="badge badge-{{ $related->status_badge }}">
                                    {{ ucfirst(str_replace('_', ' ', $related->status)) }}
                                </span>
                            </td>
                            <td>
                                <a href="{{ route('owner.payments.show', $related->id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Gateway Response -->
@if($transaction->gateway_response && !empty($transaction->gateway_response))
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-code mr-2"></i>
                    Gateway Response
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>{{ json_encode($transaction->gateway_response, JSON_PRETTY_PRINT) }}</code></pre>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('owner.payments.refund', $transaction->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h4 class="modal-title">Process Refund</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="refund_amount">Refund Amount *</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" name="refund_amount" id="refund_amount"
                                   class="form-control" step="0.01"
                                   max="{{ $transaction->net_amount }}"
                                   value="{{ $transaction->net_amount }}" required>
                        </div>
                        <small class="form-text text-muted">
                            Maximum refundable amount: ${{ number_format($transaction->net_amount, 2) }}
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="refund_reason">Reason for Refund *</label>
                        <select name="refund_reason" id="refund_reason" class="form-control" required>
                            <option value="">Select a reason</option>
                            <option value="customer_request">Customer Request</option>
                            <option value="service_cancelled">Service Cancelled</option>
                            <option value="duplicate_charge">Duplicate Charge</option>
                            <option value="fraudulent">Fraudulent Transaction</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="refund_notes">Additional Notes</label>
                        <textarea name="refund_notes" id="refund_notes" class="form-control" rows="3"
                                  placeholder="Optional refund notes..."></textarea>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Important</h6>
                        <ul class="mb-0 pl-4">
                            <li>Refunds are typically processed within 5-10 business days</li>
                            <li>Gateway fees may not be refundable</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-undo"></i> Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

pre code {
    font-size: 0.85rem;
    color: #495057;
}

.card-tools .badge {
    font-size: 0.9rem;
}
</style>
@stop

@section('js')
<script>
function printReceipt() {
    window.print();
}

function downloadReceipt() {
    // AJAX call to generate PDF receipt
    window.location.href = '/owner/payments/{{ $transaction->id }}/receipt.pdf';
}

$(document).ready(function() {
    // Initialize any additional functionality
    console.log('Transaction details page loaded');

    // Auto-calculate partial refund amounts
    $('#refund_amount').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var maxAmount = parseFloat($(this).attr('max')) || 0;

        if (amount > maxAmount) {
            $(this).val(maxAmount);
        }
    });
});
</script>
@stop
