@extends('owner.layouts.app')

@section('title', 'Payment Management')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-credit-card mr-2"></i>Payment Management</h1>
            <p class="text-muted mb-0">Manage payments, gateways, and transaction processing</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" data-toggle="modal" data-target="#manualPaymentModal">
                <i class="fas fa-plus"></i> Manual Payment
            </button>
            <a href="{{ route('owner.payments.gateways') }}" class="btn btn-primary">
                <i class="fas fa-cog"></i> Gateway Settings
            </a>
        </div>
    </div>
@stop

@section('content')
<!-- Date Range Filter -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="card-title mb-0">
                    <i class="fas fa-chart-line mr-2"></i>
                    Payment Overview
                </h3>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex justify-content-end">
                    <div class="input-group" style="width: 300px;">
                        <select name="period" class="form-control" onchange="this.form.submit()">
                            <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('period') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period') == 'month' || !request('period') ? 'selected' : '' }}>This Month</option>
                            <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>This Quarter</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Payment Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($paymentStats['total_volume'], 2) }}</h3>
                        <p class="mb-0">Total Volume</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $paymentStats['total_transactions'] }} transactions
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($paymentStats['net_revenue'], 2) }}</h3>
                        <p class="mb-0">Net Revenue</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        After fees: ${{ number_format($paymentStats['total_fees'], 2) }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ number_format($paymentStats['success_rate'], 1) }}%</h3>
                        <p class="mb-0">Success Rate</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $paymentStats['successful_transactions'] }} successful
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($paymentStats['average_transaction'], 2) }}</h3>
                        <p class="mb-0">Avg Transaction</p>
                    </div>
                    <i class="fas fa-calculator fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $paymentStats['pending_transactions'] }} pending
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gateway Performance -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Payment Trends</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="paymentTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Gateway Performance</h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Gateway</th>
                                <th class="text-center">Success</th>
                                <th class="text-right">Volume</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($gatewayMetrics as $gateway => $metrics)
                            <tr>
                                <td>
                                    <span class="badge badge-{{ $gateway == 'stripe' ? 'primary' : 'info' }}">
                                        {{ ucfirst($gateway) }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    @php
                                        $successRate = $metrics->total_transactions > 0
                                            ? ($metrics->successful_transactions / $metrics->total_transactions) * 100
                                            : 0;
                                    @endphp
                                    <span class="badge badge-{{ $successRate >= 95 ? 'success' : ($successRate >= 90 ? 'warning' : 'danger') }}">
                                        {{ number_format($successRate, 1) }}%
                                    </span>
                                </td>
                                <td class="text-right">
                                    ${{ number_format($metrics->total_volume, 0) }}
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="text-center text-muted">
                                    No gateway data available
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        @if($failedTransactions > 0)
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>{{ $failedTransactions }}</strong> failed transactions in the last 7 days.
            <a href="#" class="alert-link">Review now</a>
        </div>
        @endif
    </div>
</div>

<!-- Gateway Accounts Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Payment Gateway Status</h3>
                <div class="card-tools">
                    <a href="{{ route('owner.payments.gateways') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-cog"></i> Manage Gateways
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    @forelse($gatewayAccounts as $account)
                    <div class="col-md-6">
                        <div class="card {{ $account->is_active ? 'border-success' : 'border-warning' }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h5 class="mb-1">
                                            <i class="fab fa-{{ $account->gateway_type == 'stripe' ? 'stripe' : 'paypal' }}"></i>
                                            {{ ucfirst($account->gateway_type) }}
                                            @if($account->is_primary)
                                                <span class="badge badge-primary">Primary</span>
                                            @endif
                                        </h5>
                                        <p class="text-muted mb-2">
                                            Status:
                                            <span class="badge badge-{{ $account->status_badge }}">
                                                {{ ucfirst($account->account_status) }}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input gateway-toggle"
                                                   type="checkbox"
                                                   {{ $account->is_active ? 'checked' : '' }}
                                                   data-account-id="{{ $account->id }}">
                                        </div>
                                    </div>
                                </div>

                                @if($account->account_details)
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Connected: {{ $account->connected_at ? $account->connected_at->format('M j, Y') : 'Not connected' }}
                                    </small>
                                </div>
                                @endif

                                @if($account->formatted_fee)
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Fee: {{ $account->formatted_fee }}
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="col-12">
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5>No Payment Gateways Connected</h5>
                            <p class="text-muted">Connect Stripe or PayPal to start accepting payments</p>
                            <a href="{{ route('owner.payments.gateways') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Setup Payment Gateways
                            </a>
                        </div>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">Recent Transactions</h3>
            <div class="card-tools">
                <div class="input-group input-group-sm" style="width: 200px;">
                    <input type="text" class="form-control" placeholder="Search transactions..." id="transactionSearch">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-default">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body table-responsive p-0">
        <table class="table table-striped table-hover" id="transactionsTable">
            <thead>
                <tr>
                    <th>Transaction ID</th>
                    <th>Date</th>
                    <th>Customer</th>
                    <th>Service</th>
                    <th>Gateway</th>
                    <th>Method</th>
                    <th class="text-right">Amount</th>
                    <th>Status</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($recentTransactions as $transaction)
                <tr>
                    <td>
                        <code class="text-sm">{{ $transaction->transaction_id }}</code>
                    </td>
                    <td>
                        <span title="{{ $transaction->processed_at }}">
                            {{ $transaction->processed_at ? $transaction->processed_at->format('M j, Y H:i') : 'N/A' }}
                        </span>
                    </td>
                    <td>
                        @if($transaction->customer)
                            <a href="#" class="text-decoration-none">
                                {{ $transaction->customer->name }}
                            </a>
                        @else
                            <span class="text-muted">Guest</span>
                        @endif
                    </td>
                    <td>
                        @if($transaction->service)
                            <span class="text-sm">{{ $transaction->service->name }}</span>
                        @else
                            <span class="text-muted">N/A</span>
                        @endif
                    </td>
                    <td>
                        <span class="badge badge-{{ $transaction->gateway_type == 'stripe' ? 'primary' : ($transaction->gateway_type == 'paypal' ? 'info' : 'secondary') }}">
                            {{ ucfirst($transaction->gateway_type) }}
                        </span>
                    </td>
                    <td>
                        <span class="text-sm">{{ ucfirst(str_replace('_', ' ', $transaction->payment_method)) }}</span>
                    </td>
                    <td class="text-right">
                        <span class="font-weight-bold">
                            ${{ $transaction->formatted_amount }}
                        </span>
                        @if($transaction->gateway_fees > 0)
                            <br><small class="text-muted">
                                Fee: ${{ number_format($transaction->gateway_fees, 2) }}
                            </small>
                        @endif
                    </td>
                    <td>
                        <span class="badge badge-{{ $transaction->status_badge }}">
                            {{ ucfirst(str_replace('_', ' ', $transaction->status)) }}
                        </span>
                    </td>
                    <td class="text-center">
                        <div class="btn-group btn-group-sm">
                            <a href="{{ route('owner.payments.show', $transaction->id) }}"
                               class="btn btn-outline-primary btn-sm"
                               title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            @if($transaction->canBeRefunded())
                            <button class="btn btn-outline-warning btn-sm refund-btn"
                                    data-transaction-id="{{ $transaction->id }}"
                                    title="Process Refund">
                                <i class="fas fa-undo"></i>
                            </button>
                            @endif
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                        <br>
                        <span class="text-muted">No transactions found</span>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Manual Payment Modal -->
<div class="modal fade" id="manualPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('owner.payments.manual') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h4 class="modal-title">Process Manual Payment</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="booking_id">Booking *</label>
                        <select name="booking_id" id="booking_id" class="form-control" required>
                            <option value="">Select Booking</option>
                            <!-- Populate with unpaid bookings -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="amount">Amount *</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" name="amount" id="amount" class="form-control"
                                   step="0.01" min="0.01" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="payment_method">Payment Method *</label>
                        <select name="payment_method" id="payment_method" class="form-control" required>
                            <option value="cash">Cash</option>
                            <option value="card">Card (Terminal)</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="3"
                                  placeholder="Optional payment notes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Process Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.gateway-toggle {
    transform: scale(1.2);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

.card-body .btn-group {
    box-shadow: none;
}

.opacity-75 {
    opacity: 0.75;
}

#paymentTrendsChart {
    max-height: 300px;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize payment trends chart
    initializePaymentTrendsChart();

    // Transaction search
    $('#transactionSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#transactionsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Gateway toggle
    $('.gateway-toggle').on('change', function() {
        var accountId = $(this).data('account-id');
        var isActive = $(this).is(':checked');

        // AJAX call to toggle gateway status
        $.post('/owner/payments/gateways/' + accountId + '/toggle', {
            '_token': '{{ csrf_token() }}',
            'is_active': isActive
        }, function(response) {
            if (response.success) {
                toastr.success('Gateway status updated successfully');
            } else {
                toastr.error('Failed to update gateway status');
            }
        }).fail(function() {
            toastr.error('An error occurred while updating gateway status');
        });
    });

    // Refund button
    $('.refund-btn').on('click', function() {
        var transactionId = $(this).data('transaction-id');
        // Implement refund modal/process
        window.location.href = '/owner/payments/' + transactionId + '/refund';
    });
});

function initializePaymentTrendsChart() {
    var ctx = document.getElementById('paymentTrendsChart').getContext('2d');

    // Fetch chart data via AJAX
    $.get('/owner/payments/analytics', {
        'period': '{{ request("period", "month") }}',
        'group_by': 'day'
    }, function(data) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => new Date(item.period).toLocaleDateString()),
                datasets: [{
                    label: 'Revenue',
                    data: data.map(item => item.revenue),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Transactions',
                    data: data.map(item => item.transactions),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: false,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Revenue ($)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Transactions'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    });
}
</script>
@stop
