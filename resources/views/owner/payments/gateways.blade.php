@extends('owner.layouts.app')

@section('title', 'Payment Gateway Settings')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-cog mr-2"></i>Payment Gateway Settings</h1>
            <p class="text-muted mb-0">Connect and manage your payment gateways</p>
        </div>
        <div>
            <a href="{{ route('owner.payments.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Payments
            </a>
        </div>
    </div>
@stop

@section('content')
<div class="row">
    <!-- Stripe Connect -->
    <div class="col-md-6">
        <div class="card {{ $stripeAccount && $stripeAccount->is_active ? 'border-success' : 'border-secondary' }}">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fab fa-stripe mr-2"></i>
                    Stripe Connect Standard
                </h3>
                @if($stripeAccount && $stripeAccount->is_active)
                    <span class="badge badge-success float-right">Connected</span>
                @else
                    <span class="badge badge-secondary float-right">Not Connected</span>
                @endif
            </div>
            <div class="card-body">
                @if($stripeAccount)
                    <div class="mb-3">
                        <h6 class="text-muted">Account Status</h6>
                        <span class="badge badge-{{ $stripeAccount->account_status == 'active' ? 'success' : 'warning' }}">
                            {{ ucfirst($stripeAccount->account_status) }}
                        </span>
                    </div>

                    @if($stripeAccount->account_details)
                        <div class="mb-3">
                            <h6 class="text-muted">Account Information</h6>
                            <p class="mb-1"><strong>Account ID:</strong> {{ $stripeAccount->gateway_account_id }}</p>
                            <p class="mb-1"><strong>Country:</strong> {{ $stripeAccount->account_details['country'] ?? 'N/A' }}</p>
                            <p class="mb-1"><strong>Currency:</strong> {{ strtoupper($stripeAccount->account_details['default_currency'] ?? 'USD') }}</p>
                        </div>
                    @endif

                    <div class="mb-3">
                        <h6 class="text-muted">Processing Fees</h6>
                        <p class="mb-0">{{ $stripeAccount->fee_percentage ?? 2.9 }}% + ${{ $stripeAccount->fee_fixed ?? 0.30 }} per transaction</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Connected</h6>
                        <p class="mb-0">{{ $stripeAccount->connected_at ? $stripeAccount->connected_at->format('M j, Y \a\t g:i A') : 'Not connected' }}</p>
                    </div>

                    <div class="d-flex gap-2">
                        @if($stripeAccount->account_status !== 'active')
                            <form action="{{ route('owner.payments.stripe.connect') }}" method="POST" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-warning btn-sm">
                                    <i class="fas fa-exclamation-triangle"></i> Complete Setup
                                </button>
                            </form>
                        @endif

                        <form action="{{ route('owner.payments.stripe.disconnect') }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to disconnect your Stripe account? This will disable payment processing.')">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-unlink"></i> Disconnect
                            </button>
                        </form>
                        <form action="{{ route('owner.payments.stripe.sync') }}" method="POST" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-sync"></i> Sync Account
                            </button>
                        </form>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fab fa-stripe fa-3x text-muted mb-3"></i>
                        <h5>Connect Your Stripe Account</h5>
                        <p class="text-muted mb-3">
                            Accept credit cards, debit cards, and digital wallets with Stripe Connect Standard accounts.
                            You'll have full access to your Stripe Dashboard and direct relationship with Stripe.
                        </p>

                        <div class="mb-3">
                            <h6 class="text-muted">Features:</h6>
                            <ul class="list-unstyled text-sm">
                                <li><i class="fas fa-check text-success mr-2"></i>Credit & debit card processing</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Apple Pay & Google Pay support</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Real-time fraud protection</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Direct Stripe Dashboard access</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Full account control</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Instant payouts</li>
                            </ul>
                        </div>

                        <form action="{{ route('owner.payments.stripe.connect') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary">
                                <i class="fab fa-stripe"></i> Connect with Stripe
                            </button>
                        </form>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- PayPal Commerce Platform -->
    <div class="col-md-6">
        <div class="card {{ $paypalAccount && $paypalAccount->is_active ? 'border-success' : 'border-secondary' }}">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fab fa-paypal mr-2"></i>
                    PayPal Commerce Platform
                </h3>
                @if($paypalAccount && $paypalAccount->is_active)
                    <span class="badge badge-success float-right">Connected</span>
                @else
                    <span class="badge badge-secondary float-right">Not Connected</span>
                @endif
            </div>
            <div class="card-body">
                @if($paypalAccount)
                    <div class="mb-3">
                        <h6 class="text-muted">Account Status</h6>
                        <span class="badge badge-{{ $paypalAccount->account_status == 'active' ? 'success' : 'warning' }}">
                            {{ ucfirst($paypalAccount->account_status) }}
                        </span>
                    </div>

                    @if($paypalAccount->account_details)
                        <div class="mb-3">
                            <h6 class="text-muted">Account Information</h6>
                            <p class="mb-1"><strong>Merchant ID:</strong> {{ $paypalAccount->gateway_account_id }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ $paypalAccount->account_details['email'] ?? 'N/A' }}</p>
                            <p class="mb-1"><strong>Country:</strong> {{ $paypalAccount->account_details['country'] ?? 'N/A' }}</p>
                        </div>
                    @endif

                    <div class="mb-3">
                        <h6 class="text-muted">Processing Fees</h6>
                        <p class="mb-0">{{ $paypalAccount->fee_percentage ?? 2.9 }}% + ${{ $paypalAccount->fee_fixed ?? 0.30 }} per transaction</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Connected</h6>
                        <p class="mb-0">{{ $paypalAccount->connected_at ? $paypalAccount->connected_at->format('M j, Y \a\t g:i A') : 'Not connected' }}</p>
                    </div>

                    <div class="d-flex gap-2">
                        @if($paypalAccount->account_status !== 'active')
                            <a href="#" class="btn btn-warning btn-sm">
                                <i class="fas fa-exclamation-triangle"></i> Complete Setup
                            </a>
                        @endif

                        <button class="btn btn-outline-danger btn-sm" onclick="disconnectGateway('paypal')">
                            <i class="fas fa-unlink"></i> Disconnect
                        </button>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fab fa-paypal fa-3x text-muted mb-3"></i>
                        <h5>Connect Your PayPal Account</h5>
                        <p class="text-muted mb-3">
                            Accept PayPal, credit cards, and other payment methods with PayPal's global payment platform.
                        </p>

                        <div class="mb-3">
                            <h6 class="text-muted">Features:</h6>
                            <ul class="list-unstyled text-sm">
                                <li><i class="fas fa-check text-success mr-2"></i>PayPal wallet payments</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Credit & debit card processing</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Global payment acceptance</li>
                                <li><i class="fas fa-check text-success mr-2"></i>Buyer protection</li>
                            </ul>
                        </div>

                        <form action="{{ route('owner.payments.paypal.connect') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-info">
                                <i class="fab fa-paypal"></i> Connect with PayPal
                            </button>
                        </form>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Gateway Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sliders-h mr-2"></i>
                    Payment Settings
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ route('owner.payments.settings') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="default_currency">Default Currency</label>
                                <select name="default_currency" id="default_currency" class="form-control">
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="GBP">GBP - British Pound</option>
                                    <option value="CAD">CAD - Canadian Dollar</option>
                                    <option value="AUD">AUD - Australian Dollar</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_timeout">Payment Timeout (minutes)</label>
                                <input type="number" name="payment_timeout" id="payment_timeout"
                                       class="form-control" value="15" min="5" max="60">
                                <small class="form-text text-muted">
                                    How long customers have to complete payment before it expires
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="require_cvv" id="require_cvv"
                                           class="form-check-input" checked>
                                    <label class="form-check-label" for="require_cvv">
                                        Require CVV for card payments
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="auto_capture" id="auto_capture"
                                           class="form-check-input" checked>
                                    <label class="form-check-label" for="auto_capture">
                                        Automatically capture payments
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="payment_description">Default Payment Description</label>
                        <input type="text" name="payment_description" id="payment_description"
                               class="form-control" value="Payment for booking services"
                               placeholder="This appears on customer statements">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Test Mode Notice -->
@if(config('app.env') !== 'production')
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Test Mode</h5>
            <p class="mb-0">
                You are currently in test mode. All transactions are simulated and no real money will be processed.
                Use test card numbers provided by your payment gateway for testing.
            </p>
        </div>
    </div>
</div>
@endif
@stop

@section('css')
<style>
.gap-2 {
    gap: 0.5rem;
}

.d-flex.gap-2 > * {
    margin-right: 0.5rem;
}

.d-flex.gap-2 > *:last-child {
    margin-right: 0;
}

.card-header .badge {
    font-size: 0.75rem;
}

.list-unstyled li {
    margin-bottom: 0.25rem;
}
</style>
@stop

@section('js')
<script>
function disconnectGateway(gateway) {
    if (confirm(`Are you sure you want to disconnect your ${gateway.charAt(0).toUpperCase() + gateway.slice(1)} account? This will prevent new payments through this gateway.`)) {
        // AJAX call to disconnect gateway
        $.post(`/owner/payments/${gateway}/disconnect`, {
            '_token': '{{ csrf_token() }}'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                toastr.error(response.message || 'Failed to disconnect gateway');
            }
        }).fail(function() {
            toastr.error('An error occurred while disconnecting the gateway');
        });
    }
}

function disconnectStripe() {
    if (confirm('Are you sure you want to disconnect your Stripe account? This will prevent new payments through Stripe.')) {
        $.post('{{ route("owner.payments.stripe.disconnect") }}', {
            '_token': '{{ csrf_token() }}'
        }, function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload();
            } else {
                toastr.error(response.message || 'Failed to disconnect Stripe account');
            }
        }).fail(function() {
            toastr.error('An error occurred while disconnecting Stripe');
        });
    }
}

function syncStripeAccount() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
    btn.disabled = true;

    $.post('{{ route("owner.payments.stripe.sync") }}', {
        '_token': '{{ csrf_token() }}'
    }, function(response) {
        if (response.success) {
            toastr.success('Stripe account synced successfully');
            location.reload();
        } else {
            toastr.error(response.message || 'Failed to sync Stripe account');
        }
    }).fail(function() {
        toastr.error('An error occurred while syncing Stripe account');
    }).always(function() {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

$(document).ready(function() {
    // Initialize any additional JavaScript functionality
    console.log('Payment gateways page loaded');
});
</script>
@stop
