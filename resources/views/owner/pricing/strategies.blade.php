@extends('owner.layouts.app')

@section('title', 'Pricing Strategies')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-cogs mr-2"></i>Pricing Strategies</h1>
            <p class="text-muted mb-0">Manage dynamic pricing rules and strategies for your services</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.pricing.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pricing
            </a>
            <a href="{{ route('owner.pricing.calculator') }}" class="btn btn-outline-info">
                <i class="fas fa-calculator"></i> Price Calculator
            </a>
            <button class="btn btn-primary" data-toggle="modal" data-target="#newStrategyModal">
                <i class="fas fa-plus"></i> New Strategy
            </button>
        </div>
    </div>
@stop

@section('content')

<!-- Strategies Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $strategies->where('is_active', true)->count() }}</h3>
                        <p class="mb-0">Active Strategies</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Currently running
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $strategies->where('strategy_type', 'dynamic')->count() }}</h3>
                        <p class="mb-0">Dynamic Pricing</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Auto-adjusting strategies
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        @php
                            $averagePrice = $strategies->where('is_active', true)->avg('base_price') ?? 0;
                        @endphp
                        <h3 class="mb-0">${{ number_format($averagePrice, 2) }}</h3>
                        <p class="mb-0">Average Price</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Across all strategies
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        @php
                            $servicesWithStrategies = $strategies->whereNotNull('service_id')->pluck('service_id')->unique()->count();
                        @endphp
                        <h3 class="mb-0">{{ $servicesWithStrategies }}</h3>
                        <p class="mb-0">Services Covered</p>
                    </div>
                    <i class="fas fa-list fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        With pricing strategies
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Strategies Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">Pricing Strategies</h3>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary strategy-filter active" data-type="all">All</button>
                <button type="button" class="btn btn-outline-primary strategy-filter" data-type="dynamic">Dynamic</button>
                <button type="button" class="btn btn-outline-primary strategy-filter" data-type="seasonal">Seasonal</button>
                <button type="button" class="btn btn-outline-primary strategy-filter" data-type="demand_based">Demand-Based</button>
                <button type="button" class="btn btn-outline-primary strategy-filter" data-type="fixed">Fixed</button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover" id="strategiesTable">
                <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Service</th>
                        <th>Type</th>
                        <th class="text-center">Base Price</th>
                        <th class="text-center">Price Range</th>
                        <th class="text-center">Effective Period</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Performance</th>
                        <th class="text-right">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($strategies as $strategy)
                    <tr data-strategy-type="{{ $strategy->strategy_type }}">
                        <td>
                            <strong>{{ $strategy->strategy_name }}</strong>
                            <br>
                            <small class="text-muted">
                                Created {{ $strategy->created_at->diffForHumans() }}
                            </small>
                        </td>
                        <td>
                            @if($strategy->service)
                                <span class="text-primary">{{ $strategy->service->name }}</span>
                            @else
                                <span class="text-muted">All Services</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge badge-{{ $strategy->strategy_type == 'dynamic' ? 'success' : ($strategy->strategy_type == 'seasonal' ? 'warning' : ($strategy->strategy_type == 'demand_based' ? 'info' : 'secondary')) }}">
                                {{ ucfirst(str_replace('_', ' ', $strategy->strategy_type)) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <strong>${{ number_format($strategy->base_price, 2) }}</strong>
                        </td>
                        <td class="text-center">
                            @if($strategy->minimum_price || $strategy->maximum_price)
                                <small>
                                    ${{ number_format($strategy->minimum_price ?? $strategy->base_price, 2) }}
                                    -
                                    ${{ number_format($strategy->maximum_price ?? $strategy->base_price, 2) }}
                                </small>
                            @else
                                <span class="text-muted">Fixed</span>
                            @endif
                        </td>
                        <td class="text-center">
                            <small>
                                {{ \Carbon\Carbon::parse($strategy->effective_from)->format('M j, Y') }}
                                @if($strategy->effective_to)
                                    <br>to {{ \Carbon\Carbon::parse($strategy->effective_to)->format('M j, Y') }}
                                @else
                                    <br><span class="text-success">Ongoing</span>
                                @endif
                            </small>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-{{ $strategy->is_active ? 'success' : 'secondary' }}">
                                {{ $strategy->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            @if($strategy->is_active)
                                <br>
                                @php
                                    $now = \Carbon\Carbon::now();
                                    $effectiveFrom = \Carbon\Carbon::parse($strategy->effective_from);
                                    $effectiveTo = $strategy->effective_to ? \Carbon\Carbon::parse($strategy->effective_to) : null;
                                @endphp
                                @if($now < $effectiveFrom)
                                    <small class="text-warning">Pending</small>
                                @elseif($effectiveTo && $now > $effectiveTo)
                                    <small class="text-danger">Expired</small>
                                @else
                                    <small class="text-success">Running</small>
                                @endif
                            @endif
                        </td>
                        <td class="text-center">
                            @php
                                // Mock performance data
                                $performance = rand(5, 25);
                                $conversionRate = rand(8, 18);
                            @endphp
                            <span class="badge badge-{{ $performance > 15 ? 'success' : ($performance > 8 ? 'warning' : 'danger') }}">
                                +{{ $performance }}%
                            </span>
                            <br>
                            <small class="text-muted">{{ $conversionRate }}% conv.</small>
                        </td>
                        <td class="text-right">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm view-strategy"
                                        data-strategy-id="{{ $strategy->id }}"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm edit-strategy"
                                        data-strategy-id="{{ $strategy->id }}"
                                        title="Edit Strategy">
                                    <i class="fas fa-edit"></i>
                                </button>
                                @if($strategy->is_active)
                                    <button class="btn btn-outline-secondary btn-sm toggle-strategy"
                                            data-strategy-id="{{ $strategy->id }}"
                                            data-action="deactivate"
                                            title="Deactivate Strategy">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                @else
                                    <button class="btn btn-outline-success btn-sm toggle-strategy"
                                            data-strategy-id="{{ $strategy->id }}"
                                            data-action="activate"
                                            title="Activate Strategy">
                                        <i class="fas fa-play"></i>
                                    </button>
                                @endif
                                <button class="btn btn-outline-info btn-sm test-strategy"
                                        data-strategy-id="{{ $strategy->id }}"
                                        title="A/B Test">
                                    <i class="fas fa-flask"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm delete-strategy"
                                        data-strategy-id="{{ $strategy->id }}"
                                        title="Delete Strategy">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-cogs fa-2x mb-2"></i><br>
                            No pricing strategies found.<br>
                            <button class="btn btn-primary btn-sm mt-2" data-toggle="modal" data-target="#newStrategyModal">
                                Create Your First Strategy
                            </button>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    @if($strategies->hasPages())
    <div class="card-footer">
        {{ $strategies->links() }}
    </div>
    @endif
</div>

<!-- New Strategy Modal -->
<div class="modal fade" id="newStrategyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Create New Pricing Strategy
                </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="newStrategyForm" action="{{ route('owner.pricing.strategies.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="strategy_name">Strategy Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="strategy_name" name="strategy_name" required
                                       placeholder="e.g., Peak Hours Premium">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="strategy_type">Strategy Type <span class="text-danger">*</span></label>
                                <select class="form-control" id="strategy_type" name="strategy_type" required>
                                    <option value="">Select type...</option>
                                    <option value="fixed">Fixed Pricing</option>
                                    <option value="dynamic">Dynamic Pricing</option>
                                    <option value="seasonal">Seasonal Pricing</option>
                                    <option value="demand_based">Demand-Based</option>
                                    <option value="package">Package Deal</option>
                                    <option value="tier_based">Tier-Based</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="service_id">Apply to Service (Optional)</label>
                        <select class="form-control" id="service_id" name="service_id">
                            <option value="">All Services</option>
                            @foreach(\App\Models\Service::where('business_id', auth()->user()->ownedBusinesses()->first()->id ?? 0)->where('is_active', true)->get() as $service)
                                <option value="{{ $service->id }}">
                                    {{ $service->name }} - ${{ number_format($service->price, 2) }}
                                </option>
                            @endforeach
                        </select>
                        <small class="form-text text-muted">Leave blank to apply to all services</small>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="base_price">Base Price <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="base_price" name="base_price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="minimum_price">Minimum Price</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="minimum_price" name="minimum_price"
                                           step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted">Optional floor price</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="maximum_price">Maximum Price</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="maximum_price" name="maximum_price"
                                           step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted">Optional ceiling price</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="effective_from">Effective From <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="effective_from" name="effective_from" required
                                       min="{{ date('Y-m-d') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="effective_to">Effective To</label>
                                <input type="date" class="form-control" id="effective_to" name="effective_to">
                                <small class="form-text text-muted">Leave blank for ongoing strategy</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="strategy_description">Description (Optional)</label>
                        <textarea class="form-control" id="strategy_description" name="strategy_description"
                                  rows="3" placeholder="Describe the pricing strategy and conditions..."></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" checked>
                        <label class="form-check-label" for="is_active">
                            Activate immediately
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Strategy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@stop

@section('adminlte_js')
@parent
<script>
$(document).ready(function() {
    // Filter strategies
    $('.strategy-filter').click(function() {
        $('.strategy-filter').removeClass('active');
        $(this).addClass('active');

        const type = $(this).data('type');
        const rows = $('#strategiesTable tbody tr');

        if (type === 'all') {
            rows.show();
        } else {
            rows.hide();
            rows.filter('[data-strategy-type="' + type + '"]').show();
        }
    });

    // Auto-fill base price when service is selected
    $('#service_id').change(function() {
        const selectedOption = $(this).find(':selected');
        const serviceText = selectedOption.text();
        if (serviceText.includes('$')) {
            const price = serviceText.split('$')[1];
            $('#base_price').val(price);
        }
    });

    // Set minimum effective_to date when effective_from changes
    $('#effective_from').change(function() {
        const fromDate = new Date($(this).val());
        fromDate.setDate(fromDate.getDate() + 1); // Minimum 1 day duration
        const minToDate = fromDate.toISOString().split('T')[0];
        $('#effective_to').attr('min', minToDate);

        if ($('#effective_to').val() && $('#effective_to').val() <= $(this).val()) {
            $('#effective_to').val(minToDate);
        }
    });

    // Strategy actions
    $('.toggle-strategy').click(function() {
        const strategyId = $(this).data('strategy-id');
        const action = $(this).data('action');
        const actionText = action === 'activate' ? 'activate' : 'deactivate';

        if (confirm(`Are you sure you want to ${actionText} this strategy?`)) {
            updateStrategyStatus(strategyId, action === 'activate');
        }
    });

    $('.delete-strategy').click(function() {
        const strategyId = $(this).data('strategy-id');
        if (confirm('Are you sure you want to delete this strategy? This action cannot be undone.')) {
            deleteStrategy(strategyId);
        }
    });

    function updateStrategyStatus(strategyId, isActive) {
        $.ajax({
            url: `/owner/pricing/strategies/${strategyId}`,
            method: 'PUT',
            data: {
                is_active: isActive,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                toastr.success('Strategy status updated successfully');
                location.reload();
            },
            error: function(xhr) {
                toastr.error('Error updating strategy status');
            }
        });
    }

    function deleteStrategy(strategyId) {
        $.ajax({
            url: `/owner/pricing/strategies/${strategyId}`,
            method: 'DELETE',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                toastr.success('Strategy deleted successfully');
                location.reload();
            },
            error: function(xhr) {
                toastr.error('Error deleting strategy');
            }
        });
    }

    // View strategy details
    $('.view-strategy').click(function() {
        const strategyId = $(this).data('strategy-id');
        // Implement view functionality
        toastr.info('Strategy details view coming soon!');
    });

    // Edit strategy
    $('.edit-strategy').click(function() {
        const strategyId = $(this).data('strategy-id');
        // Implement edit functionality
        toastr.info('Strategy editing coming soon!');
    });

    // Test strategy
    $('.test-strategy').click(function() {
        const strategyId = $(this).data('strategy-id');
        // Redirect to experiments page with pre-filled data
        window.location.href = '{{ route("owner.pricing.experiments") }}?strategy_id=' + strategyId;
    });

    // Form validation
    $('#newStrategyForm').on('submit', function(e) {
        const effectiveFrom = new Date($('#effective_from').val());
        const effectiveTo = $('#effective_to').val();

        if (effectiveTo && new Date(effectiveTo) <= effectiveFrom) {
            e.preventDefault();
            toastr.error('Effective to date must be after effective from date');
            return false;
        }

        const basePrice = parseFloat($('#base_price').val());
        const minPrice = $('#minimum_price').val();
        const maxPrice = $('#maximum_price').val();

        if (minPrice && parseFloat(minPrice) > basePrice) {
            e.preventDefault();
            toastr.error('Minimum price cannot be higher than base price');
            return false;
        }

        if (maxPrice && parseFloat(maxPrice) < basePrice) {
            e.preventDefault();
            toastr.error('Maximum price cannot be lower than base price');
            return false;
        }
    });

    // Set default effective from date to today
    $('#effective_from').val(new Date().toISOString().split('T')[0]);
});
</script>
@stop
