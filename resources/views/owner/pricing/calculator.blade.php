@extends('owner.layouts.app')

@section('title', 'Pricing Calculator')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-calculator mr-2"></i>Pricing Calculator</h1>
            <p class="text-muted mb-0">Calculate dynamic prices based on demand, time, and service parameters</p>
        </div>
        <div>
            <a href="{{ route('owner.pricing.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pricing
            </a>
        </div>
    </div>
@stop

@section('content')

<!-- Calculator Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Price Calculator
                </h3>
            </div>
            <div class="card-body">
                <form id="priceCalculatorForm">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_id">Select Service <span class="text-danger">*</span></label>
                                <select class="form-control" id="service_id" name="service_id" required>
                                    <option value="">Choose a service...</option>
                                    @foreach($services as $service)
                                        <option value="{{ $service->id }}" data-base-price="{{ $service->price }}">
                                            {{ $service->name }} - ${{ number_format($service->price, 2) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date">Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date" name="date" required min="{{ date('Y-m-d') }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="time">Time <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="time" name="time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="demand_factor">Demand Factor</label>
                                <div class="input-group">
                                    <input type="range" class="form-control-range" id="demand_factor" name="demand_factor"
                                           min="0.5" max="2.0" step="0.1" value="1.0">
                                    <div class="input-group-append">
                                        <span class="input-group-text demand-factor-display">1.0x</span>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    Adjust for high/low demand periods (0.5x - 2.0x)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calculator"></i> Calculate Price
                            </button>
                            <button type="reset" class="btn btn-outline-secondary ml-2">
                                <i class="fas fa-redo"></i> Reset
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Price Result -->
        <div class="card" id="priceResultCard" style="display: none;">
            <div class="card-header bg-success text-white">
                <h3 class="card-title">
                    <i class="fas fa-dollar-sign mr-2"></i>
                    Calculated Price
                </h3>
            </div>
            <div class="card-body text-center">
                <div class="price-display">
                    <h2 class="text-success mb-3" id="calculatedPrice">$0.00</h2>
                    <div class="price-breakdown">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Base Price:</span>
                            <span id="basePrice">$0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Demand Factor:</span>
                            <span id="demandFactorDisplay">1.0x</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Strategy:</span>
                            <span id="strategyType" class="badge badge-info">Fixed</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between font-weight-bold">
                            <span>Final Price:</span>
                            <span id="finalPrice" class="text-success">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Presets -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-clock mr-2"></i>
                    Quick Presets
                </h3>
            </div>
            <div class="card-body">
                <div class="btn-group-vertical btn-block">
                    <button type="button" class="btn btn-outline-primary demand-preset" data-factor="0.8" data-time="09:00">
                        <i class="fas fa-sunrise"></i> Morning (0.8x)
                    </button>
                    <button type="button" class="btn btn-outline-warning demand-preset" data-factor="1.2" data-time="12:00">
                        <i class="fas fa-sun"></i> Peak Hours (1.2x)
                    </button>
                    <button type="button" class="btn btn-outline-info demand-preset" data-factor="1.0" data-time="15:00">
                        <i class="fas fa-cloud-sun"></i> Afternoon (1.0x)
                    </button>
                    <button type="button" class="btn btn-outline-secondary demand-preset" data-factor="0.9" data-time="18:00">
                        <i class="fas fa-moon"></i> Evening (0.9x)
                    </button>
                    <button type="button" class="btn btn-outline-success demand-preset" data-factor="1.5" data-time="20:00">
                        <i class="fas fa-star"></i> Premium Time (1.5x)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Calculations -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-2"></i>
                    Recent Calculations
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" id="clearHistory">
                        <i class="fas fa-trash"></i> Clear History
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="calculationHistory">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Date & Time</th>
                                <th>Demand Factor</th>
                                <th>Base Price</th>
                                <th>Final Price</th>
                                <th>Strategy</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-muted">
                                <td colspan="7" class="text-center">No calculations yet. Use the calculator above to get started.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@stop

@section('adminlte_js')
@parent
<script>
$(document).ready(function() {
    // Update demand factor display
    $('#demand_factor').on('input', function() {
        $('.demand-factor-display').text($(this).val() + 'x');
    });

    // Preset buttons
    $('.demand-preset').click(function() {
        const factor = $(this).data('factor');
        const time = $(this).data('time');

        $('#demand_factor').val(factor);
        $('#time').val(time);
        $('.demand-factor-display').text(factor + 'x');
    });

    // Form submission
    $('#priceCalculatorForm').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            service_id: $('#service_id').val(),
            date: $('#date').val(),
            time: $('#time').val(),
            demand_factor: $('#demand_factor').val(),
            _token: $('input[name="_token"]').val()
        };

        // Validate required fields
        if (!formData.service_id || !formData.date || !formData.time) {
            toastr.error('Please fill in all required fields.');
            return;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Calculating...').prop('disabled', true);

        $.ajax({
            url: '{{ route("owner.pricing.calculate") }}',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Update price display
                    $('#basePrice').text('$' + response.base_price.toFixed(2));
                    $('#demandFactorDisplay').text(response.demand_factor + 'x');
                    $('#strategyType').text(response.strategy_type.charAt(0).toUpperCase() + response.strategy_type.slice(1));
                    $('#calculatedPrice').text('$' + response.calculated_price.toFixed(2));
                    $('#finalPrice').text('$' + response.calculated_price.toFixed(2));

                    // Show result card
                    $('#priceResultCard').slideDown();

                    // Add to history
                    addToHistory(formData, response);

                    toastr.success('Price calculated successfully!');
                } else {
                    toastr.error('Error calculating price. Please try again.');
                }
            },
            error: function(xhr) {
                console.error('Calculation error:', xhr);
                toastr.error('Error calculating price. Please check your inputs and try again.');
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Add calculation to history
    function addToHistory(formData, response) {
        const serviceName = $('#service_id option:selected').text().split(' - ')[0];
        const dateTime = formData.date + ' ' + formData.time;

        const historyRow = `
            <tr>
                <td>${serviceName}</td>
                <td>${dateTime}</td>
                <td>${response.demand_factor}x</td>
                <td>$${response.base_price.toFixed(2)}</td>
                <td class="font-weight-bold text-success">$${response.calculated_price.toFixed(2)}</td>
                <td><span class="badge badge-info">${response.strategy_type}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary recalculate-btn"
                            data-service="${formData.service_id}"
                            data-date="${formData.date}"
                            data-time="${formData.time}"
                            data-demand="${formData.demand_factor}">
                        <i class="fas fa-redo"></i>
                    </button>
                </td>
            </tr>
        `;

        const tbody = $('#calculationHistory tbody');

        // Remove "no calculations" message if present
        if (tbody.find('tr.text-muted').length > 0) {
            tbody.empty();
        }

        tbody.prepend(historyRow);

        // Limit history to 10 entries
        const rows = tbody.find('tr');
        if (rows.length > 10) {
            rows.slice(10).remove();
        }
    }

    // Recalculate from history
    $(document).on('click', '.recalculate-btn', function() {
        const serviceId = $(this).data('service');
        const date = $(this).data('date');
        const time = $(this).data('time');
        const demand = $(this).data('demand');

        $('#service_id').val(serviceId);
        $('#date').val(date);
        $('#time').val(time);
        $('#demand_factor').val(demand);
        $('.demand-factor-display').text(demand + 'x');

        // Scroll to top and trigger calculation
        $('html, body').animate({scrollTop: 0}, 500);
        $('#priceCalculatorForm').submit();
    });

    // Clear history
    $('#clearHistory').click(function() {
        if (confirm('Are you sure you want to clear the calculation history?')) {
            $('#calculationHistory tbody').html(`
                <tr class="text-muted">
                    <td colspan="7" class="text-center">No calculations yet. Use the calculator above to get started.</td>
                </tr>
            `);
            toastr.info('Calculation history cleared.');
        }
    });

    // Form reset
    $('#priceCalculatorForm').on('reset', function() {
        $('#priceResultCard').slideUp();
        $('#demand_factor').val(1.0);
        $('.demand-factor-display').text('1.0x');
    });

    // Set default date to today
    $('#date').val(new Date().toISOString().split('T')[0]);
});
</script>
@stop