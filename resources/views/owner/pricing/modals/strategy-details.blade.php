<!-- Strategy Details Modal -->
<div class="modal fade" id="strategyDetailsModal" tabindex="-1" role="dialog" aria-labelledby="strategyDetailsModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="strategyDetailsModalLabel">
                    <i class="fas fa-chart-line mr-2"></i>Strategy Details
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Strategy Header -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h3 id="strategy_detail_name">Strategy Name</h3>
                        <p class="text-muted mb-2" id="strategy_detail_description">Strategy description</p>
                        <div>
                            <span class="badge badge-primary" id="strategy_detail_type">Strategy Type</span>
                            <span class="badge badge-success ml-2" id="strategy_detail_status">Active</span>
                            <span class="badge badge-info ml-2" id="strategy_detail_priority">Priority: Medium</span>
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group">
                            <button class="btn btn-outline-primary btn-sm" id="editStrategyDetailBtn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-outline-success btn-sm" id="testStrategyDetailBtn">
                                <i class="fas fa-flask"></i> A/B Test
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-gradient-success text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1" id="detail_revenue">$0.00</h3>
                                <small>Revenue Generated</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-info text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1" id="detail_bookings">0</h3>
                                <small>Total Bookings</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-warning text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1" id="detail_conversion">0%</h3>
                                <small>Conversion Rate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-primary text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1" id="detail_avg_price">$0.00</h3>
                                <small>Average Price</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Strategy Configuration -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div id="strategy_config_content">
                                    <!-- Dynamic content based on strategy type -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Performance Trend</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="strategyPerformanceChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Applied Services -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Applied Services</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="appliedServicesTable">
                                        <thead>
                                            <tr>
                                                <th>Service</th>
                                                <th>Original Price</th>
                                                <th>Current Price</th>
                                                <th>Bookings</th>
                                                <th>Revenue</th>
                                                <th>Performance</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Dynamic content -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Recent Activity</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline" id="strategy_activity_timeline">
                                    <!-- Dynamic timeline content -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Strategy Settings</h5>
                            </div>
                            <div class="card-body">
                                <dl class="row">
                                    <dt class="col-sm-5">Created:</dt>
                                    <dd class="col-sm-7" id="detail_created_at">-</dd>

                                    <dt class="col-sm-5">Last Modified:</dt>
                                    <dd class="col-sm-7" id="detail_updated_at">-</dd>

                                    <dt class="col-sm-5">Auto-Adjust:</dt>
                                    <dd class="col-sm-7" id="detail_auto_adjust">No</dd>

                                    <dt class="col-sm-5">Days Active:</dt>
                                    <dd class="col-sm-7" id="detail_days_active">0</dd>

                                    <dt class="col-sm-5">Applied To:</dt>
                                    <dd class="col-sm-7" id="detail_applied_to">All Services</dd>
                                </dl>

                                <div class="mt-3" id="detail_notes_section" style="display: none;">
                                    <h6>Notes:</h6>
                                    <p class="text-muted" id="detail_notes"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-danger" id="deactivateStrategyBtn">
                    <i class="fas fa-pause mr-1"></i>Deactivate
                </button>
                <button type="button" class="btn btn-primary" id="editStrategyFromDetailBtn">
                    <i class="fas fa-edit mr-1"></i>Edit Strategy
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Function to view strategy details
function viewStrategyDetails(strategyId) {
    // Show loading state
    $('#strategyDetailsModal .modal-body').html('<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading strategy details...</p></div>');
    $('#strategyDetailsModal').modal('show');

    // For now, show placeholder data since the routes don't exist yet
    setTimeout(function() {
        var dummyStrategy = {
            id: strategyId,
            strategy_name: 'Sample Dynamic Pricing Strategy',
            description: 'This is a sample strategy for demonstration purposes',
            strategy_type: 'dynamic',
            is_active: true,
            priority: 2,
            base_price: 50.00,
            minimum_price: 30.00,
            maximum_price: 100.00,
            demand_multiplier: 1.5,
            low_demand_discount: 15,
            auto_adjust: true,
            notes: 'Sample strategy notes for demonstration',
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString(),
            performance_metrics: {
                revenue: 5280.50,
                bookings: 42,
                conversion_rate: 18.5,
                average_price: 65.25
            },
            applied_services: [
                { name: 'Premium Service', original_price: 50.00, current_price: 65.00, bookings: 25, revenue: 1625.00, performance: 85.5 },
                { name: 'Standard Service', original_price: 40.00, current_price: 52.00, bookings: 17, revenue: 884.00, performance: 72.3 }
            ],
            recent_activity: [
                { title: 'Strategy Created', description: 'Dynamic pricing strategy was created', created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() },
                { title: 'Price Adjusted', description: 'Prices adjusted due to high demand', created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() }
            ]
        };

        populateStrategyDetails(dummyStrategy);
    }, 1000);

    /* TODO: Uncomment when routes are implemented
    // Fetch strategy details
    $.get(`/owner/pricing/strategies/${strategyId}/details`, function(data) {
        populateStrategyDetails(data);
    }).fail(function() {
        $('#strategyDetailsModal .modal-body').html('<div class="text-center py-5 text-danger"><i class="fas fa-exclamation-triangle fa-2x"></i><p class="mt-2">Error loading strategy details</p></div>');
    });
    */
}

function populateStrategyDetails(strategy) {
    // Restore original modal content
    restoreModalContent();

    // Basic information
    $('#strategy_detail_name').text(strategy.strategy_name);
    $('#strategy_detail_description').text(strategy.description || 'No description provided');
    $('#strategy_detail_type').text(formatStrategyType(strategy.strategy_type));
    $('#strategy_detail_status').text(strategy.is_active ? 'Active' : 'Inactive')
                                 .removeClass('badge-success badge-secondary')
                                 .addClass(strategy.is_active ? 'badge-success' : 'badge-secondary');
    $('#strategy_detail_priority').text('Priority: ' + formatPriority(strategy.priority));

    // Performance metrics
    const metrics = strategy.performance_metrics || {};
    $('#detail_revenue').text('$' + (parseFloat(metrics.revenue || 0)).toLocaleString());
    $('#detail_bookings').text(metrics.bookings || 0);
    $('#detail_conversion').text((metrics.conversion_rate || 0).toFixed(1) + '%');
    $('#detail_avg_price').text('$' + (parseFloat(metrics.average_price || 0)).toFixed(2));

    // Configuration
    populateStrategyConfiguration(strategy);

    // Applied services
    populateAppliedServices(strategy.applied_services || []);

    // Settings
    $('#detail_created_at').text(formatDate(strategy.created_at));
    $('#detail_updated_at').text(formatDate(strategy.updated_at));
    $('#detail_auto_adjust').text(strategy.auto_adjust ? 'Yes' : 'No');
    $('#detail_applied_to').text(strategy.service_id ? strategy.service_name : 'All Services');

    // Calculate days active
    if (strategy.created_at) {
        const daysActive = Math.floor((new Date() - new Date(strategy.created_at)) / (1000 * 60 * 60 * 24));
        $('#detail_days_active').text(daysActive);
    }

    // Notes
    if (strategy.notes) {
        $('#detail_notes').text(strategy.notes);
        $('#detail_notes_section').show();
    } else {
        $('#detail_notes_section').hide();
    }

    // Recent activity
    populateRecentActivity(strategy.recent_activity || []);

    // Performance chart
    if (strategy.performance_trend) {
        initializePerformanceChart(strategy.performance_trend);
    }

    // Button actions
    $('#editStrategyDetailBtn, #editStrategyFromDetailBtn').off('click').on('click', function() {
        $('#strategyDetailsModal').modal('hide');
        editStrategy(strategy.id);
    });

    $('#testStrategyDetailBtn').off('click').on('click', function() {
        $('#strategyDetailsModal').modal('hide');
        startABTest(strategy.id);
    });

    $('#deactivateStrategyBtn').off('click').on('click', function() {
        if (confirm('Are you sure you want to ' + (strategy.is_active ? 'deactivate' : 'activate') + ' this strategy?')) {
            toggleStrategyStatus(strategy.id, !strategy.is_active);
        }
    });

    // Update deactivate button text
    $('#deactivateStrategyBtn').html(
        '<i class="fas fa-' + (strategy.is_active ? 'pause' : 'play') + ' mr-1"></i>' +
        (strategy.is_active ? 'Deactivate' : 'Activate')
    ).removeClass('btn-outline-danger btn-outline-success')
     .addClass(strategy.is_active ? 'btn-outline-danger' : 'btn-outline-success');
}

function populateStrategyConfiguration(strategy) {
    let configHtml = '';

    switch (strategy.strategy_type) {
        case 'fixed':
            configHtml = `
                <dl class="row mb-0">
                    <dt class="col-sm-4">Fixed Price:</dt>
                    <dd class="col-sm-8">$${parseFloat(strategy.fixed_price || strategy.base_price || 0).toFixed(2)}</dd>
                </dl>
            `;
            break;

        case 'dynamic':
        case 'demand_based':
            configHtml = `
                <dl class="row mb-0">
                    <dt class="col-sm-4">Base Price:</dt>
                    <dd class="col-sm-8">$${parseFloat(strategy.base_price || 0).toFixed(2)}</dd>
                    <dt class="col-sm-4">Price Range:</dt>
                    <dd class="col-sm-8">$${parseFloat(strategy.minimum_price || 0).toFixed(2)} - $${parseFloat(strategy.maximum_price || 0).toFixed(2)}</dd>
                    <dt class="col-sm-4">Demand Multiplier:</dt>
                    <dd class="col-sm-8">${strategy.demand_multiplier || 1}x</dd>
                    <dt class="col-sm-4">Low Demand Discount:</dt>
                    <dd class="col-sm-8">${strategy.low_demand_discount || 0}%</dd>
                </dl>
            `;
            break;

        case 'seasonal':
            configHtml = `
                <dl class="row mb-0">
                    <dt class="col-sm-4">Base Price:</dt>
                    <dd class="col-sm-8">$${parseFloat(strategy.seasonal_base_price || strategy.base_price || 0).toFixed(2)}</dd>
                    <dt class="col-sm-4">Season:</dt>
                    <dd class="col-sm-8">${formatDate(strategy.season_start)} - ${formatDate(strategy.season_end)}</dd>
                    <dt class="col-sm-4">Multiplier:</dt>
                    <dd class="col-sm-8">${strategy.seasonal_multiplier || 1}x</dd>
                    <dt class="col-sm-4">Repeat Yearly:</dt>
                    <dd class="col-sm-8">${strategy.repeat_yearly ? 'Yes' : 'No'}</dd>
                </dl>
            `;
            break;

        default:
            configHtml = '<p class="text-muted">No configuration details available</p>';
    }

    $('#strategy_config_content').html(configHtml);
}

function populateAppliedServices(services) {
    const tbody = $('#appliedServicesTable tbody');
    tbody.empty();

    if (services.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-3">
                    No services data available
                </td>
            </tr>
        `);
        return;
    }

    services.forEach(function(service) {
        const performanceClass = service.performance >= 80 ? 'text-success' : (service.performance >= 60 ? 'text-warning' : 'text-danger');

        tbody.append(`
            <tr>
                <td><strong>${service.name}</strong></td>
                <td>$${parseFloat(service.original_price || 0).toFixed(2)}</td>
                <td>$${parseFloat(service.current_price || 0).toFixed(2)}</td>
                <td>${service.bookings || 0}</td>
                <td>$${parseFloat(service.revenue || 0).toLocaleString()}</td>
                <td>
                    <span class="${performanceClass}">
                        ${(service.performance || 0).toFixed(1)}%
                    </span>
                </td>
            </tr>
        `);
    });
}

function populateRecentActivity(activities) {
    const timeline = $('#strategy_activity_timeline');
    timeline.empty();

    if (activities.length === 0) {
        timeline.append('<p class="text-muted">No recent activity</p>');
        return;
    }

    activities.forEach(function(activity, index) {
        const isLast = index === activities.length - 1;
        timeline.append(`
            <div class="timeline-item ${isLast ? 'timeline-item-last' : ''}">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                    <h6 class="timeline-title">${activity.title}</h6>
                    <p class="timeline-text">${activity.description}</p>
                    <small class="text-muted">${formatDate(activity.created_at)}</small>
                </div>
            </div>
        `);
    });
}

function initializePerformanceChart(data) {
    const ctx = document.getElementById('strategyPerformanceChart').getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => formatDate(item.date)),
            datasets: [{
                label: 'Revenue',
                data: data.map(item => item.revenue),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                }
            }
        }
    });
}

function toggleStrategyStatus(strategyId, isActive) {
    toastr.info('Strategy status toggle functionality will be available when backend routes are implemented');
    $('#strategyDetailsModal').modal('hide');

    /* TODO: Uncomment when routes are implemented
    $.ajax({
        url: `/owner/pricing/strategies/${strategyId}/toggle`,
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            is_active: isActive
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message || 'Strategy status updated successfully!');
                $('#strategyDetailsModal').modal('hide');
                location.reload();
            } else {
                toastr.error(response.message || 'Error updating strategy status');
            }
        },
        error: function() {
            toastr.error('Error updating strategy status. Please try again.');
        }
    });
    */
}

function restoreModalContent() {
    // This function restores the original modal content structure
    // Implementation would restore the full HTML structure shown above
}

// Utility functions
function formatStrategyType(type) {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatPriority(priority) {
    const priorities = { 1: 'Low', 2: 'Medium', 3: 'High', 4: 'Critical' };
    return priorities[priority] || 'Medium';
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
    border-left: 2px solid #e9ecef;
}

.timeline-item-last {
    border-left: none;
}

.timeline-marker {
    position: absolute;
    left: -6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    margin-left: 20px;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
