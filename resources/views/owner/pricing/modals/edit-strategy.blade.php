<!-- Edit Strategy Modal -->
<div class="modal fade" id="editStrategyModal" tabindex="-1" role="dialog" aria-labelledby="editStrategyModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="editStrategyModalLabel">
                    <i class="fas fa-edit mr-2"></i>Edit Pricing Strategy
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editStrategyForm" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit_strategy_id" name="strategy_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_strategy_name">Strategy Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_strategy_name" name="strategy_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_strategy_type">Strategy Type <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_strategy_type" name="strategy_type" required>
                                    <option value="fixed">Fixed Pricing</option>
                                    <option value="dynamic">Dynamic Pricing</option>
                                    <option value="seasonal">Seasonal Pricing</option>
                                    <option value="demand_based">Demand-Based Pricing</option>
                                    <option value="time_based">Time-Based Pricing</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_service_id">Apply to Service</label>
                                <select class="form-control" id="edit_service_id" name="service_id">
                                    <option value="">All Services</option>
                                    @if(isset($services))
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}">{{ $service->name }} (${{ number_format($service->price, 2) }})</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_priority">Priority</label>
                                <select class="form-control" id="edit_priority" name="priority">
                                    <option value="1">Low</option>
                                    <option value="2">Medium</option>
                                    <option value="3">High</option>
                                    <option value="4">Critical</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Configuration for Edit -->
                    <div id="editPricingConfiguration">
                        <!-- Fixed Pricing Edit -->
                        <div id="editFixedPricing" class="edit-pricing-config d-none">
                            <h5 class="mt-3 mb-3">Fixed Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_fixed_price">Fixed Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="edit_fixed_price" name="fixed_price">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_fixed_description">Description</label>
                                        <input type="text" class="form-control" id="edit_fixed_description" name="description">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Pricing Edit -->
                        <div id="editDynamicPricing" class="edit-pricing-config d-none">
                            <h5 class="mt-3 mb-3">Dynamic Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_base_price">Base Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="edit_base_price" name="base_price">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_minimum_price">Minimum Price</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="edit_minimum_price" name="minimum_price">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_maximum_price">Maximum Price</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="edit_maximum_price" name="maximum_price">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_demand_multiplier">Demand Multiplier</label>
                                        <input type="number" step="0.01" class="form-control" id="edit_demand_multiplier" name="demand_multiplier">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_low_demand_discount">Low Demand Discount (%)</label>
                                        <input type="number" step="1" class="form-control" id="edit_low_demand_discount" name="low_demand_discount" min="0" max="50">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Seasonal Pricing Edit -->
                        <div id="editSeasonalPricing" class="edit-pricing-config d-none">
                            <h5 class="mt-3 mb-3">Seasonal Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_season_start">Season Start Date</label>
                                        <input type="date" class="form-control" id="edit_season_start" name="season_start">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="edit_season_end">Season End Date</label>
                                        <input type="date" class="form-control" id="edit_season_end" name="season_end">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_seasonal_base_price">Base Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="edit_seasonal_base_price" name="seasonal_base_price">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_seasonal_multiplier">Seasonal Multiplier</label>
                                        <input type="number" step="0.01" class="form-control" id="edit_seasonal_multiplier" name="seasonal_multiplier">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="edit_repeat_yearly">Repeat Yearly</label>
                                        <select class="form-control" id="edit_repeat_yearly" name="repeat_yearly">
                                            <option value="1">Yes</option>
                                            <option value="0">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Stats -->
                    <div class="mt-4" id="strategyPerformance">
                        <h5>Strategy Performance</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="mb-1" id="performance_revenue">$0.00</h4>
                                        <small class="text-muted">Revenue Generated</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="mb-1" id="performance_bookings">0</h4>
                                        <small class="text-muted">Bookings</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="mb-1" id="performance_conversion">0%</h4>
                                        <small class="text-muted">Conversion Rate</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 class="mb-1" id="performance_days_active">0</h4>
                                        <small class="text-muted">Days Active</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Settings -->
                    <div class="mt-4">
                        <h5>Strategy Settings</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="edit_is_active" name="is_active">
                                        <label class="custom-control-label" for="edit_is_active">Strategy Active</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="edit_auto_adjust" name="auto_adjust">
                                        <label class="custom-control-label" for="edit_auto_adjust">Enable Auto-Adjustment</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="edit_notes">Notes</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" id="deleteStrategyBtn">
                        <i class="fas fa-trash mr-1"></i>Delete Strategy
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>Update Strategy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Function to edit strategy
function editStrategy(strategyId) {
    // For now, show a placeholder modal since the routes don't exist yet
    // Populate with dummy data for demonstration
    $('#edit_strategy_id').val(strategyId);
    $('#edit_strategy_name').val('Sample Strategy');
    $('#edit_strategy_type').val('fixed');
    $('#edit_service_id').val('');
    $('#edit_priority').val(2);
    $('#edit_notes').val('This is a sample strategy for demonstration');

    // Set form action (will be handled in form submission)
    $('#editStrategyForm').attr('action', `/owner/pricing/strategies/${strategyId}`);

    // Set checkboxes
    $('#edit_is_active').prop('checked', true);
    $('#edit_auto_adjust').prop('checked', false);

    // Show fixed pricing configuration
    $('.edit-pricing-config').addClass('d-none');
    $('#editFixedPricing').removeClass('d-none');
    $('#edit_fixed_price').val('50.00');
    $('#edit_fixed_description').val('Sample pricing');

    // Update performance metrics with dummy data
    $('#performance_revenue').text('$1,250.00');
    $('#performance_bookings').text('25');
    $('#performance_conversion').text('12.5%');
    $('#performance_days_active').text('30');

    // Show modal
    $('#editStrategyModal').modal('show');

    /* TODO: Uncomment when routes are implemented
    // Fetch strategy data
    $.get(`/owner/pricing/strategies/${strategyId}`, function(strategy) {
        // Populate basic form fields
        $('#edit_strategy_id').val(strategy.id);
        $('#edit_strategy_name').val(strategy.strategy_name);
        $('#edit_strategy_type').val(strategy.strategy_type);
        $('#edit_service_id').val(strategy.service_id || '');
        $('#edit_priority').val(strategy.priority || 2);
        $('#edit_notes').val(strategy.notes || '');

        // Set form action
        $('#editStrategyForm').attr('action', `/owner/pricing/strategies/${strategy.id}`);

        // Set checkboxes
        $('#edit_is_active').prop('checked', strategy.is_active);
        $('#edit_auto_adjust').prop('checked', strategy.auto_adjust);

        // Show relevant pricing configuration
        $('.edit-pricing-config').addClass('d-none');

        if (strategy.strategy_type === 'fixed') {
            $('#editFixedPricing').removeClass('d-none');
            $('#edit_fixed_price').val(strategy.fixed_price || strategy.base_price);
            $('#edit_fixed_description').val(strategy.description);
        } else if (strategy.strategy_type === 'dynamic' || strategy.strategy_type === 'demand_based') {
            $('#editDynamicPricing').removeClass('d-none');
            $('#edit_base_price').val(strategy.base_price);
            $('#edit_minimum_price').val(strategy.minimum_price);
            $('#edit_maximum_price').val(strategy.maximum_price);
            $('#edit_demand_multiplier').val(strategy.demand_multiplier);
            $('#edit_low_demand_discount').val(strategy.low_demand_discount);
        } else if (strategy.strategy_type === 'seasonal') {
            $('#editSeasonalPricing').removeClass('d-none');
            $('#edit_season_start').val(strategy.season_start);
            $('#edit_season_end').val(strategy.season_end);
            $('#edit_seasonal_base_price').val(strategy.seasonal_base_price || strategy.base_price);
            $('#edit_seasonal_multiplier').val(strategy.seasonal_multiplier);
            $('#edit_repeat_yearly').val(strategy.repeat_yearly ? '1' : '0');
        }

        // Update performance metrics
        if (strategy.performance_metrics) {
            $('#performance_revenue').text('$' + parseFloat(strategy.performance_metrics.revenue || 0).toLocaleString());
            $('#performance_bookings').text(strategy.performance_metrics.bookings || 0);
            $('#performance_conversion').text((strategy.performance_metrics.conversion_rate || 0).toFixed(1) + '%');
        }

        // Calculate days active
        if (strategy.created_at) {
            const daysActive = Math.floor((new Date() - new Date(strategy.created_at)) / (1000 * 60 * 60 * 24));
            $('#performance_days_active').text(daysActive);
        }

        // Show modal
        $('#editStrategyModal').modal('show');
    }).fail(function() {
        toastr.error('Error loading strategy data');
    });
    */
}

$(document).ready(function() {
    // Strategy type change handler for edit modal
    $('#edit_strategy_type').on('change', function() {
        var strategyType = $(this).val();

        // Hide all edit pricing configs
        $('.edit-pricing-config').addClass('d-none');

        // Show relevant pricing config
        if (strategyType === 'fixed') {
            $('#editFixedPricing').removeClass('d-none');
        } else if (strategyType === 'dynamic' || strategyType === 'demand_based') {
            $('#editDynamicPricing').removeClass('d-none');
        } else if (strategyType === 'seasonal') {
            $('#editSeasonalPricing').removeClass('d-none');
        }
    });

    // Edit form submission
    $('#editStrategyForm').on('submit', function(e) {
        e.preventDefault();

        toastr.info('Strategy update functionality will be available when backend routes are implemented');
        $('#editStrategyModal').modal('hide');

        /* TODO: Uncomment when routes are implemented
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i>Updating...').prop('disabled', true);

        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || 'Pricing strategy updated successfully!');
                    $('#editStrategyModal').modal('hide');
                    location.reload();
                } else {
                    toastr.error(response.message || 'Error updating pricing strategy');
                }
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors;
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        toastr.error(errors[key][0]);
                    });
                } else {
                    toastr.error('Error updating pricing strategy. Please try again.');
                }
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
        */
    });

    // Delete strategy
    $('#deleteStrategyBtn').on('click', function() {
        if (confirm('Are you sure you want to delete this pricing strategy? This action cannot be undone.')) {
            toastr.info('Strategy deletion functionality will be available when backend routes are implemented');
            $('#editStrategyModal').modal('hide');

            /* TODO: Uncomment when routes are implemented
            var strategyId = $('#edit_strategy_id').val();

            $.ajax({
                url: `/owner/pricing/strategies/${strategyId}`,
                method: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message || 'Pricing strategy deleted successfully!');
                        $('#editStrategyModal').modal('hide');
                        location.reload();
                    } else {
                        toastr.error(response.message || 'Error deleting pricing strategy');
                    }
                },
                error: function() {
                    toastr.error('Error deleting pricing strategy. Please try again.');
                }
            });
            */
        }
    });
});
</script>
