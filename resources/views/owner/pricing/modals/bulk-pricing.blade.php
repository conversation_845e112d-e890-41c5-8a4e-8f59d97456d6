<!-- Bulk Pricing Modal -->
<div class="modal fade" id="bulkPricingModal" tabindex="-1" role="dialog" aria-labelledby="bulkPricingModalLabel">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="bulkPricingModalLabel">
                    <i class="fas fa-layer-group mr-2"></i>Bulk Pricing Update
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Bulk Actions Panel -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Bulk Actions</h5>
                            </div>
                            <div class="card-body">
                                <form id="bulkPricingForm">
                                    @csrf

                                    <!-- Action Type -->
                                    <div class="form-group">
                                        <label for="bulk_action_type">Action Type</label>
                                        <select class="form-control" id="bulk_action_type" name="action_type" required>
                                            <option value="">Select Action</option>
                                            <option value="percentage_increase">Percentage Increase</option>
                                            <option value="percentage_decrease">Percentage Decrease</option>
                                            <option value="fixed_amount_increase">Fixed Amount Increase</option>
                                            <option value="fixed_amount_decrease">Fixed Amount Decrease</option>
                                            <option value="set_fixed_price">Set Fixed Price</option>
                                            <option value="round_prices">Round Prices</option>
                                        </select>
                                    </div>

                                    <!-- Action Value -->
                                    <div class="form-group" id="actionValueGroup" style="display: none;">
                                        <label for="bulk_action_value" id="actionValueLabel">Value</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend" id="valuePrefix" style="display: none;">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="bulk_action_value" name="action_value" placeholder="0.00">
                                            <div class="input-group-append" id="valueSuffix" style="display: none;">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted" id="actionHelperText"></small>
                                    </div>

                                    <!-- Round to -->
                                    <div class="form-group" id="roundToGroup" style="display: none;">
                                        <label for="round_to">Round to</label>
                                        <select class="form-control" id="round_to" name="round_to">
                                            <option value="0.01">Nearest cent ($0.01)</option>
                                            <option value="0.05">Nearest nickel ($0.05)</option>
                                            <option value="0.10">Nearest dime ($0.10)</option>
                                            <option value="0.25">Nearest quarter ($0.25)</option>
                                            <option value="0.50">Nearest half dollar ($0.50)</option>
                                            <option value="1.00">Nearest dollar ($1.00)</option>
                                            <option value="5.00">Nearest $5</option>
                                            <option value="10.00">Nearest $10</option>
                                        </select>
                                    </div>

                                    <!-- Apply to -->
                                    <div class="form-group">
                                        <label for="apply_to">Apply to</label>
                                        <select class="form-control" id="apply_to" name="apply_to" required>
                                            <option value="selected">Selected Services</option>
                                            <option value="all">All Services</option>
                                            <option value="category">By Category</option>
                                            <option value="price_range">By Price Range</option>
                                        </select>
                                    </div>

                                    <!-- Category Filter -->
                                    <div class="form-group" id="categoryGroup" style="display: none;">
                                        <label for="category_id">Category</label>
                                        <select class="form-control" id="category_id" name="category_id">
                                            <option value="">Select Category</option>
                                            @if(isset($serviceCategories))
                                                @foreach($serviceCategories as $category)
                                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>

                                    <!-- Price Range Filter -->
                                    <div id="priceRangeGroup" style="display: none;">
                                        <div class="form-group">
                                            <label for="min_price">Minimum Price</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number" step="0.01" class="form-control" id="min_price" name="min_price" placeholder="0.00">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="max_price">Maximum Price</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="number" step="0.01" class="form-control" id="max_price" name="max_price" placeholder="999.99">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Preview & Apply -->
                                    <div class="form-group">
                                        <button type="button" class="btn btn-info btn-block" id="previewChanges">
                                            <i class="fas fa-eye mr-1"></i>Preview Changes
                                        </button>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-block" disabled id="applyChanges">
                                            <i class="fas fa-save mr-1"></i>Apply Changes
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Services List -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Services</h5>
                                <div class="card-tools">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="selectAllServices">
                                        <label class="form-check-label" for="selectAllServices">
                                            Select All
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0" style="max-height: 500px; overflow-y: auto;">
                                <table class="table table-sm table-hover" id="servicesTable">
                                    <thead class="thead-light">
                                        <tr>
                                            <th width="50px">
                                                <input type="checkbox" id="selectAllTableServices">
                                            </th>
                                            <th>Service Name</th>
                                            <th width="120px">Current Price</th>
                                            <th width="120px">New Price</th>
                                            <th width="100px">Change</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($services))
                                            @foreach($services as $service)
                                            <tr data-service-id="{{ $service->id }}">
                                                <td>
                                                    <input type="checkbox" class="service-checkbox" value="{{ $service->id }}" name="selected_services[]">
                                                </td>
                                                <td>
                                                    <strong>{{ $service->name }}</strong>
                                                    @if($service->category)
                                                        <br><small class="text-muted">{{ $service->category->name }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="current-price">${{ number_format($service->price, 2) }}</span>
                                                </td>
                                                <td>
                                                    <span class="new-price text-muted">-</span>
                                                </td>
                                                <td>
                                                    <span class="price-change text-muted">-</span>
                                                </td>
                                            </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="5" class="text-center text-muted py-3">
                                                    No services available
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Section -->
                <div class="row mt-3" id="changeSummary" style="display: none;">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle mr-2"></i>Change Summary</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Services affected:</strong> <span id="affectedCount">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Average change:</strong> <span id="averageChange">$0.00</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Total revenue impact:</strong> <span id="revenueImpact">$0.00</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Price range:</strong> <span id="newPriceRange">$0.00 - $0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="resetPreview">
                    <i class="fas fa-undo mr-1"></i>Reset Preview
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Action type change handler
    $('#bulk_action_type').on('change', function() {
        var actionType = $(this).val();

        // Reset UI
        $('#actionValueGroup, #roundToGroup').hide();
        $('#valuePrefix, #valueSuffix').hide();
        $('#applyChanges').prop('disabled', true);

        // Configure UI based on action type
        switch(actionType) {
            case 'percentage_increase':
            case 'percentage_decrease':
                $('#actionValueGroup').show();
                $('#valueSuffix').show();
                $('#actionValueLabel').text('Percentage');
                $('#actionHelperText').text('Enter percentage to ' + (actionType.includes('increase') ? 'increase' : 'decrease') + ' prices');
                break;

            case 'fixed_amount_increase':
            case 'fixed_amount_decrease':
                $('#actionValueGroup').show();
                $('#valuePrefix').show();
                $('#actionValueLabel').text('Amount');
                $('#actionHelperText').text('Enter dollar amount to ' + (actionType.includes('increase') ? 'add to' : 'subtract from') + ' prices');
                break;

            case 'set_fixed_price':
                $('#actionValueGroup').show();
                $('#valuePrefix').show();
                $('#actionValueLabel').text('New Price');
                $('#actionHelperText').text('Enter the new price for all selected services');
                break;

            case 'round_prices':
                $('#roundToGroup').show();
                $('#actionHelperText').text('Round all selected service prices');
                break;
        }
    });

    // Apply to change handler
    $('#apply_to').on('change', function() {
        var applyTo = $(this).val();

        $('#categoryGroup, #priceRangeGroup').hide();

        switch(applyTo) {
            case 'category':
                $('#categoryGroup').show();
                break;
            case 'price_range':
                $('#priceRangeGroup').show();
                break;
        }
    });

    // Select all functionality
    $('#selectAllServices, #selectAllTableServices').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('.service-checkbox').prop('checked', isChecked);

        // Sync both checkboxes
        $('#selectAllServices, #selectAllTableServices').prop('checked', isChecked);
    });

    // Individual service selection
    $(document).on('change', '.service-checkbox', function() {
        var totalServices = $('.service-checkbox').length;
        var checkedServices = $('.service-checkbox:checked').length;

        $('#selectAllServices, #selectAllTableServices').prop('checked', totalServices === checkedServices);
    });

    // Preview changes
    $('#previewChanges').on('click', function() {
        var formData = $('#bulkPricingForm').serialize();
        var selectedServices = [];

        // Get selected services based on apply_to setting
        var applyTo = $('#apply_to').val();

        if (applyTo === 'selected') {
            $('.service-checkbox:checked').each(function() {
                selectedServices.push($(this).val());
            });

            if (selectedServices.length === 0) {
                toastr.warning('Please select at least one service');
                return;
            }
        }

        // Show loading
        $(this).html('<i class="fas fa-spinner fa-spin mr-1"></i>Calculating...').prop('disabled', true);

        // For now, show a placeholder response since the routes don't exist yet
        setTimeout(function() {
            toastr.info('Bulk pricing preview functionality will be available when backend routes are implemented');
            $('#previewChanges').html('<i class="fas fa-eye mr-1"></i>Preview Changes').prop('disabled', false);
        }, 1000);

        /* TODO: Uncomment when routes are implemented
        $.ajax({
            url: '/owner/pricing/bulk-preview',
            method: 'POST',
            data: formData + '&selected_services=' + selectedServices.join(','),
            success: function(response) {
                if (response.success) {
                    updatePreview(response.data);
                    $('#changeSummary').show();
                    $('#applyChanges').prop('disabled', false);
                } else {
                    toastr.error(response.message || 'Error calculating price changes');
                }
            },
            error: function() {
                toastr.error('Error calculating price changes. Please try again.');
            },
            complete: function() {
                $('#previewChanges').html('<i class="fas fa-eye mr-1"></i>Preview Changes').prop('disabled', false);
            }
        });
        */
    });

    // Apply changes
    $('#bulkPricingForm').on('submit', function(e) {
        e.preventDefault();

        toastr.info('Bulk pricing update functionality will be available when backend routes are implemented');

        /* TODO: Uncomment when routes are implemented
        if (!confirm('Are you sure you want to apply these pricing changes? This action cannot be undone.')) {
            return;
        }

        var form = $(this);
        var submitBtn = $('#applyChanges');
        var originalText = submitBtn.html();

        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i>Applying...').prop('disabled', true);

        $.ajax({
            url: '/owner/pricing/bulk-update',
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || 'Pricing changes applied successfully!');
                    $('#bulkPricingModal').modal('hide');
                    location.reload();
                } else {
                    toastr.error(response.message || 'Error applying pricing changes');
                }
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors;
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        toastr.error(errors[key][0]);
                    });
                } else {
                    toastr.error('Error applying pricing changes. Please try again.');
                }
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
        */
    });

    // Reset preview
    $('#resetPreview').on('click', function() {
        $('.new-price').text('-').removeClass('text-success text-danger text-warning').addClass('text-muted');
        $('.price-change').text('-').removeClass('text-success text-danger text-warning').addClass('text-muted');
        $('#changeSummary').hide();
        $('#applyChanges').prop('disabled', true);
    });

    // Modal reset on close
    $('#bulkPricingModal').on('hidden.bs.modal', function() {
        $('#bulkPricingForm')[0].reset();
        $('#actionValueGroup, #roundToGroup, #categoryGroup, #priceRangeGroup, #changeSummary').hide();
        $('.service-checkbox').prop('checked', false);
        $('#selectAllServices, #selectAllTableServices').prop('checked', false);
        $('#resetPreview').click();
    });
});

function updatePreview(data) {
    var affectedCount = 0;
    var totalChange = 0;
    var minPrice = Number.MAX_SAFE_INTEGER;
    var maxPrice = 0;

    // Update each service row
    data.services.forEach(function(service) {
        var row = $(`tr[data-service-id="${service.id}"]`);
        var newPriceCell = row.find('.new-price');
        var changeCell = row.find('.price-change');

        if (service.new_price !== null) {
            affectedCount++;

            // Update new price
            newPriceCell.text('$' + parseFloat(service.new_price).toFixed(2))
                        .removeClass('text-muted text-success text-danger text-warning');

            // Calculate and display change
            var change = service.new_price - service.current_price;
            var changePercent = ((change / service.current_price) * 100);

            totalChange += change;
            minPrice = Math.min(minPrice, service.new_price);
            maxPrice = Math.max(maxPrice, service.new_price);

            // Format change display
            var changeText = (change >= 0 ? '+' : '') + '$' + change.toFixed(2) +
                           ' (' + (changePercent >= 0 ? '+' : '') + changePercent.toFixed(1) + '%)';

            changeCell.text(changeText).removeClass('text-muted');

            // Color coding
            if (change > 0) {
                newPriceCell.addClass('text-success');
                changeCell.addClass('text-success');
            } else if (change < 0) {
                newPriceCell.addClass('text-danger');
                changeCell.addClass('text-danger');
            } else {
                newPriceCell.addClass('text-warning');
                changeCell.addClass('text-warning');
            }
        }
    });

    // Update summary
    $('#affectedCount').text(affectedCount);
    $('#averageChange').text((affectedCount > 0 ? (totalChange >= 0 ? '+' : '') + '$' + (totalChange / affectedCount).toFixed(2) : '$0.00'));
    $('#revenueImpact').text((totalChange >= 0 ? '+' : '') + '$' + Math.abs(totalChange).toFixed(2));
    $('#newPriceRange').text('$' + (minPrice === Number.MAX_SAFE_INTEGER ? '0.00' : minPrice.toFixed(2)) +
                           ' - $' + (maxPrice === 0 ? '0.00' : maxPrice.toFixed(2)));
}
</script>
