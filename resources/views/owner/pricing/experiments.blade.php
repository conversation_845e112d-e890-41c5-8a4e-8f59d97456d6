@extends('owner.layouts.app')

@section('title', 'Pricing Experiments')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-flask mr-2"></i>Pricing Experiments</h1>
            <p class="text-muted mb-0">A/B test different pricing strategies to optimize revenue</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.pricing.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pricing
            </a>
            <a href="{{ route('owner.pricing.calculator') }}" class="btn btn-outline-info">
                <i class="fas fa-calculator"></i> Price Calculator
            </a>
            <button class="btn btn-primary" data-toggle="modal" data-target="#newExperimentModal">
                <i class="fas fa-plus"></i> New Experiment
            </button>
        </div>
    </div>
@stop

@section('content')

<!-- Experiments Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $experiments->where('status', 'running')->count() }}</h3>
                        <p class="mb-0">Running Tests</p>
                    </div>
                    <i class="fas fa-play fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Active experiments
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $experiments->where('status', 'completed')->count() }}</h3>
                        <p class="mb-0">Completed</p>
                    </div>
                    <i class="fas fa-check fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Finished experiments
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $experiments->where('status', 'paused')->count() }}</h3>
                        <p class="mb-0">Paused</p>
                    </div>
                    <i class="fas fa-pause fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Temporarily stopped
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        @php
                            $totalSampleSize = $experiments->where('status', 'running')->sum('sample_size');
                        @endphp
                        <h3 class="mb-0">{{ number_format($totalSampleSize) }}</h3>
                        <p class="mb-0">Total Sample Size</p>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Across all experiments
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Experiments Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">Pricing Experiments</h3>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-primary experiment-filter active" data-status="all">All</button>
                <button type="button" class="btn btn-outline-primary experiment-filter" data-status="running">Running</button>
                <button type="button" class="btn btn-outline-primary experiment-filter" data-status="completed">Completed</button>
                <button type="button" class="btn btn-outline-primary experiment-filter" data-status="paused">Paused</button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover" id="experimentsTable">
                <thead>
                    <tr>
                        <th>Experiment</th>
                        <th>Service</th>
                        <th class="text-center">Control Price</th>
                        <th class="text-center">Variant Price</th>
                        <th class="text-center">Duration</th>
                        <th class="text-center">Sample Size</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Performance</th>
                        <th class="text-right">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($experiments as $experiment)
                    <tr data-experiment-status="{{ $experiment->status }}">
                        <td>
                            <strong>{{ $experiment->experiment_name }}</strong>
                            <br>
                            <small class="text-muted">
                                Created {{ $experiment->created_at->diffForHumans() }}
                            </small>
                        </td>
                        <td>
                            <span class="text-primary">
                                {{ $experiment->service->name ?? 'Unknown Service' }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-secondary">
                                ${{ number_format($experiment->control_price, 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-primary">
                                ${{ number_format($experiment->variant_price, 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <small>
                                {{ \Carbon\Carbon::parse($experiment->start_date)->format('M j') }} -
                                {{ \Carbon\Carbon::parse($experiment->end_date)->format('M j, Y') }}
                            </small>
                            <br>
                            @php
                                $totalDays = \Carbon\Carbon::parse($experiment->start_date)->diffInDays(\Carbon\Carbon::parse($experiment->end_date));
                                $daysRemaining = max(0, \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($experiment->end_date), false));
                            @endphp
                            <small class="text-muted">
                                {{ $daysRemaining > 0 ? $daysRemaining . ' days left' : 'Ended' }}
                            </small>
                        </td>
                        <td class="text-center">
                            <strong>{{ number_format($experiment->sample_size) }}</strong>
                            <br>
                            @php
                                $progress = $experiment->current_participants ?? 0;
                                $percentage = $experiment->sample_size > 0 ? min(100, ($progress / $experiment->sample_size) * 100) : 0;
                            @endphp
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" role="progressbar" style="width: {{ $percentage }}%"></div>
                            </div>
                            <small class="text-muted">{{ number_format($progress) }} participants</small>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-{{ $experiment->status == 'running' ? 'success' : ($experiment->status == 'completed' ? 'primary' : ($experiment->status == 'paused' ? 'warning' : 'secondary')) }}">
                                {{ ucfirst($experiment->status) }}
                            </span>
                        </td>
                        <td class="text-center">
                            @php
                                $performance = $experiment->results ?? [];
                                $conversionImprovement = $performance['conversion_improvement'] ?? 0;
                                $revenueImprovement = $performance['revenue_improvement'] ?? 0;
                            @endphp
                            @if($experiment->status == 'completed' && ($conversionImprovement != 0 || $revenueImprovement != 0))
                                <span class="badge badge-{{ $revenueImprovement > 0 ? 'success' : 'danger' }}">
                                    {{ $revenueImprovement > 0 ? '+' : '' }}{{ number_format($revenueImprovement, 1) }}%
                                </span>
                                <br>
                                <small class="text-muted">revenue</small>
                            @elseif($experiment->status == 'running')
                                <span class="badge badge-info">
                                    Running
                                </span>
                                <br>
                                <small class="text-muted">{{ number_format(rand(45, 75), 1) }}% conf.</small>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td class="text-right">
                            <div class="btn-group btn-group-sm">
                                @if($experiment->status == 'running')
                                    <button class="btn btn-outline-warning btn-sm pause-experiment"
                                            data-experiment-id="{{ $experiment->id }}"
                                            title="Pause Experiment">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm stop-experiment"
                                            data-experiment-id="{{ $experiment->id }}"
                                            title="Stop Experiment">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                @elseif($experiment->status == 'paused')
                                    <button class="btn btn-outline-success btn-sm resume-experiment"
                                            data-experiment-id="{{ $experiment->id }}"
                                            title="Resume Experiment">
                                        <i class="fas fa-play"></i>
                                    </button>
                                @endif
                                <button class="btn btn-outline-primary btn-sm view-experiment"
                                        data-experiment-id="{{ $experiment->id }}"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if($experiment->status == 'completed')
                                    <button class="btn btn-outline-info btn-sm export-results"
                                            data-experiment-id="{{ $experiment->id }}"
                                            title="Export Results">
                                        <i class="fas fa-download"></i>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-flask fa-2x mb-2"></i><br>
                            No pricing experiments found.<br>
                            <button class="btn btn-primary btn-sm mt-2" data-toggle="modal" data-target="#newExperimentModal">
                                Start Your First Experiment
                            </button>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    @if($experiments->hasPages())
    <div class="card-footer">
        {{ $experiments->links() }}
    </div>
    @endif
</div>

<!-- New Experiment Modal -->
<div class="modal fade" id="newExperimentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <i class="fas fa-flask mr-2"></i>
                    Create New Pricing Experiment
                </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="newExperimentForm" action="{{ route('owner.pricing.experiments.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="experiment_name">Experiment Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="experiment_name" name="experiment_name" required
                                       placeholder="e.g., Summer Price Test">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_id">Service <span class="text-danger">*</span></label>
                                <select class="form-control" id="service_id" name="service_id" required>
                                    <option value="">Select a service...</option>
                                    @foreach(\App\Models\Service::where('business_id', auth()->user()->ownedBusinesses()->first()->id ?? 0)->where('is_active', true)->get() as $service)
                                        <option value="{{ $service->id }}">
                                            {{ $service->name }} - ${{ number_format($service->price, 2) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="control_price">Control Price (Current) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="control_price" name="control_price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="variant_price">Variant Price (Test) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="variant_price" name="variant_price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required
                                       min="{{ date('Y-m-d') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="sample_size">Target Sample Size <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="sample_size" name="sample_size"
                               min="10" value="50" required>
                        <small class="form-text text-muted">
                            Minimum 10 participants recommended for statistical significance
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="experiment_description">Description (Optional)</label>
                        <textarea class="form-control" id="experiment_description" name="experiment_description"
                                  rows="3" placeholder="Describe the hypothesis and expected outcomes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-flask"></i> Start Experiment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@stop

@section('adminlte_js')
@parent
<script>
$(document).ready(function() {
    // Filter experiments
    $('.experiment-filter').click(function() {
        $('.experiment-filter').removeClass('active');
        $(this).addClass('active');

        const status = $(this).data('status');
        const rows = $('#experimentsTable tbody tr');

        if (status === 'all') {
            rows.show();
        } else {
            rows.hide();
            rows.filter('[data-experiment-status="' + status + '"]').show();
        }
    });

    // Auto-fill control price when service is selected
    $('#service_id').change(function() {
        const selectedOption = $(this).find(':selected');
        const serviceText = selectedOption.text();
        if (serviceText.includes('$')) {
            const price = serviceText.split('$')[1];
            $('#control_price').val(price);
        }
    });

    // Set minimum end date when start date changes
    $('#start_date').change(function() {
        const startDate = new Date($(this).val());
        startDate.setDate(startDate.getDate() + 1); // Minimum 1 day duration
        const minEndDate = startDate.toISOString().split('T')[0];
        $('#end_date').attr('min', minEndDate);

        if ($('#end_date').val() && $('#end_date').val() <= $(this).val()) {
            $('#end_date').val(minEndDate);
        }
    });

    // Experiment actions
    $('.pause-experiment').click(function() {
        const experimentId = $(this).data('experiment-id');
        if (confirm('Are you sure you want to pause this experiment?')) {
            updateExperimentStatus(experimentId, 'paused');
        }
    });

    $('.resume-experiment').click(function() {
        const experimentId = $(this).data('experiment-id');
        if (confirm('Are you sure you want to resume this experiment?')) {
            updateExperimentStatus(experimentId, 'running');
        }
    });

    $('.stop-experiment').click(function() {
        const experimentId = $(this).data('experiment-id');
        if (confirm('Are you sure you want to stop this experiment? This action cannot be undone.')) {
            updateExperimentStatus(experimentId, 'completed');
        }
    });

    function updateExperimentStatus(experimentId, status) {
        $.ajax({
            url: `/owner/pricing/experiments/${experimentId}`,
            method: 'PUT',
            data: {
                status: status,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                toastr.success('Experiment status updated successfully');
                location.reload();
            },
            error: function(xhr) {
                toastr.error('Error updating experiment status');
            }
        });
    }

    // View experiment details
    $('.view-experiment').click(function() {
        const experimentId = $(this).data('experiment-id');
        // Implement view functionality
        toastr.info('Experiment details view coming soon!');
    });

    // Export results
    $('.export-results').click(function() {
        const experimentId = $(this).data('experiment-id');
        // Implement export functionality
        toastr.info('Export functionality coming soon!');
    });

    // Form validation
    $('#newExperimentForm').on('submit', function(e) {
        const startDate = new Date($('#start_date').val());
        const endDate = new Date($('#end_date').val());

        if (endDate <= startDate) {
            e.preventDefault();
            toastr.error('End date must be after start date');
            return false;
        }

        const controlPrice = parseFloat($('#control_price').val());
        const variantPrice = parseFloat($('#variant_price').val());

        if (controlPrice === variantPrice) {
            e.preventDefault();
            toastr.error('Variant price must be different from control price');
            return false;
        }
    });

    // Set default start date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    $('#start_date').val(tomorrow.toISOString().split('T')[0]);

    // Set default end date to 1 week later
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 8);
    $('#end_date').val(nextWeek.toISOString().split('T')[0]);
});
</script>
@stop
