@extends('owner.layouts.app')

@section('title', 'Customer Insights Analytics')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-users mr-2"></i>Customer Insights</h1>
            <p class="text-muted mb-0">Analyze customer behavior and spending patterns</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <a href="{{ route('owner.revenue.export') }}" class="btn btn-primary">
                <i class="fas fa-download"></i> Export Data
            </a>
        </div>
    </div>
@stop

@section('content')
<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="card-title mb-0">
                    <i class="fas fa-filter mr-2"></i>
                    Customer Insights Dashboard
                </h3>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex justify-content-end">
                    <div class="input-group" style="width: 300px;">
                        <select name="period" class="form-control" onchange="this.form.submit()">
                            <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('period') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period') == 'month' || !request('period') ? 'selected' : '' }}>This Month</option>
                            <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>This Quarter</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Customer Metrics Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($customerMetrics['total_customers']))
                                {{ number_format($customerMetrics['total_customers']) }}
                            @else
                                0
                            @endif
                        </h3>
                        <p class="mb-0">Total Customers</p>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($customerMetrics['average_customer_value']))
                                ${{ number_format($customerMetrics['average_customer_value'], 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Avg Customer Value</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($customerMetrics['repeat_customers']))
                                {{ number_format($customerMetrics['repeat_customers']) }}
                            @else
                                0
                            @endif
                        </h3>
                        <p class="mb-0">Repeat Customers</p>
                    </div>
                    <i class="fas fa-redo fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($customerMetrics['retention_rate']))
                                {{ number_format($customerMetrics['retention_rate'], 1) }}%
                            @else
                                0%
                            @endif
                        </h3>
                        <p class="mb-0">Retention Rate</p>
                    </div>
                    <i class="fas fa-heart fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Segmentation -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-pie mr-2"></i>Customer Segmentation
                </h3>
            </div>
            <div class="card-body">
                @if(isset($customerSegmentation) && count($customerSegmentation) > 0)
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="customerSegmentChart" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <div class="list-group list-group-flush">
                                @foreach($customerSegmentation as $segment)
                                <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                    <div>
                                        <strong>{{ $segment['segment'] ?? 'N/A' }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $segment['description'] ?? '' }}</small>
                                    </div>
                                    <div class="text-right">
                                        <span class="badge badge-primary badge-pill">
                                            {{ $segment['customer_count'] ?? 0 }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            ${{ number_format($segment['avg_spent'] ?? 0, 2) }} avg
                                        </small>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No customer segmentation data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line mr-2"></i>Customer Retention
                </h3>
            </div>
            <div class="card-body">
                @if(isset($customerRetention) && count($customerRetention) > 0)
                    <canvas id="retentionChart" height="200"></canvas>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No retention data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Top Customers Table -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-crown mr-2"></i>Top Customers by Revenue
        </h3>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Customer</th>
                        <th class="text-center">Visits</th>
                        <th class="text-right">Total Spent</th>
                        <th class="text-right">Avg per Visit</th>
                        <th class="text-center">Last Visit</th>
                        <th class="text-center">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($customerMetrics['top_customers']) && count($customerMetrics['top_customers']) > 0)
                        @foreach($customerMetrics['top_customers'] as $index => $customer)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="customer-avatar bg-{{ ['primary', 'success', 'info', 'warning', 'danger'][$index % 5] }} rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                        <span class="text-white font-weight-bold">
                                            {{ substr($customer['name'] ?? 'N', 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <strong>{{ $customer['name'] ?? 'N/A' }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $customer['email'] ?? '' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-primary">{{ $customer['visit_count'] ?? 0 }}</span>
                            </td>
                            <td class="text-right">
                                <strong>${{ number_format($customer['total_spent'] ?? 0, 2) }}</strong>
                            </td>
                            <td class="text-right">
                                <span class="text-muted">
                                    ${{ number_format(($customer['total_spent'] ?? 0) / max($customer['visit_count'] ?? 1, 1), 2) }}
                                </span>
                            </td>
                            <td class="text-center">
                                <small class="text-muted">
                                    {{ isset($customer['last_visit']) ? \Carbon\Carbon::parse($customer['last_visit'])->diffForHumans() : 'N/A' }}
                                </small>
                            </td>
                            <td class="text-center">
                                @php
                                    $daysSinceLastVisit = isset($customer['last_visit']) ?
                                        \Carbon\Carbon::parse($customer['last_visit'])->diffInDays(now()) : 999;

                                    if ($daysSinceLastVisit < 30) {
                                        $status = ['text' => 'Active', 'class' => 'success'];
                                    } elseif ($daysSinceLastVisit < 90) {
                                        $status = ['text' => 'Regular', 'class' => 'warning'];
                                    } else {
                                        $status = ['text' => 'Inactive', 'class' => 'danger'];
                                    }
                                @endphp
                                <span class="badge badge-{{ $status['class'] }}">{{ $status['text'] }}</span>
                            </td>
                        </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No customer data available</p>
                            </td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Customer Behavior Insights -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Customer Behavior Patterns</h3>
            </div>
            <div class="card-body">
                @if(isset($customerMetrics['behavior_patterns']))
                    <div class="row">
                        @foreach($customerMetrics['behavior_patterns'] as $pattern)
                        <div class="col-6 mb-3">
                            <div class="info-box bg-light">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas {{ $pattern['icon'] ?? 'fa-chart-bar' }}"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{{ $pattern['label'] ?? 'N/A' }}</span>
                                    <span class="info-box-number">{{ $pattern['value'] ?? 'N/A' }}</span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No behavior patterns available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Customer Acquisition</h3>
            </div>
            <div class="card-body">
                @if(isset($customerMetrics['acquisition_data']))
                    <canvas id="acquisitionChart" height="150"></canvas>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No acquisition data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-chart-line"></i><br>
                    Revenue Overview
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.analytics') }}" class="btn btn-outline-secondary btn-block">
                    <i class="fas fa-microscope"></i><br>
                    Advanced Analytics
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.service-performance') }}" class="btn btn-outline-info btn-block">
                    <i class="fas fa-chart-bar"></i><br>
                    Service Performance
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.export') }}" class="btn btn-outline-warning btn-block">
                    <i class="fas fa-download"></i><br>
                    Export Reports
                </a>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.opacity-75 {
    opacity: 0.75;
}

.customer-avatar {
    font-size: 14px;
}

.info-box {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.info-box-content {
    padding: 0.75rem;
}

.list-group-item {
    padding: 0.75rem 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    initializeCustomerSegmentChart();
    initializeRetentionChart();
    initializeAcquisitionChart();
});

function initializeCustomerSegmentChart() {
    @if(isset($customerSegmentation) && count($customerSegmentation) > 0)
    var ctx = document.getElementById('customerSegmentChart').getContext('2d');
    var segmentData = @json($customerSegmentation);

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: segmentData.map(item => item.segment || 'N/A'),
            datasets: [{
                data: segmentData.map(item => item.customer_count || 0),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    @endif
}

function initializeRetentionChart() {
    @if(isset($customerRetention) && count($customerRetention) > 0)
    var ctx = document.getElementById('retentionChart').getContext('2d');
    var retentionData = @json($customerRetention);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: retentionData.map(item => item.period || 'N/A'),
            datasets: [{
                label: 'Retention Rate',
                data: retentionData.map(item => item.retention_rate || 0),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Retention %'
                    }
                }
            }
        }
    });
    @endif
}

function initializeAcquisitionChart() {
    @if(isset($customerMetrics['acquisition_data']) && count($customerMetrics['acquisition_data']) > 0)
    var ctx = document.getElementById('acquisitionChart').getContext('2d');
    var acquisitionData = @json($customerMetrics['acquisition_data']);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: acquisitionData.map(item => item.period || 'N/A'),
            datasets: [{
                label: 'New Customers',
                data: acquisitionData.map(item => item.new_customers || 0),
                backgroundColor: '#007bff',
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'New Customers'
                    }
                }
            }
        }
    });
    @endif
}
</script>
@stop
