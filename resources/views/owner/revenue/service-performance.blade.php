@extends('owner.layouts.app')

@section('title', 'Service Performance Analytics')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-chart-bar mr-2"></i>Service Performance</h1>
            <p class="text-muted mb-0">Analyze revenue and performance by service</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <a href="{{ route('owner.revenue.export') }}" class="btn btn-primary">
                <i class="fas fa-download"></i> Export Data
            </a>
        </div>
    </div>
@stop

@section('content')
<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="card-title mb-0">
                    <i class="fas fa-filter mr-2"></i>
                    Service Performance Dashboard
                </h3>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex justify-content-end">
                    <div class="input-group" style="width: 300px;">
                        <select name="period" class="form-control" onchange="this.form.submit()">
                            <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('period') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period') == 'month' || !request('period') ? 'selected' : '' }}>This Month</option>
                            <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>This Quarter</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Service Performance Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($services) && count($services) > 0)
                                {{ $services->count() }}
                            @else
                                0
                            @endif
                        </h3>
                        <p class="mb-0">Total Services</p>
                    </div>
                    <i class="fas fa-concierge-bell fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($services) && count($services) > 0)
                                ${{ number_format($services->sum('total_revenue'), 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Total Revenue</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($services) && count($services) > 0)
                                {{ $services->sum('total_transactions') }}
                            @else
                                0
                            @endif
                        </h3>
                        <p class="mb-0">Total Bookings</p>
                    </div>
                    <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($services) && count($services) > 0 && $services->sum('total_transactions') > 0)
                                ${{ number_format($services->sum('total_revenue') / $services->sum('total_transactions'), 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Avg per Booking</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Service Performance Table -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-table mr-2"></i>Service Performance Details
        </h3>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th class="text-center">Bookings</th>
                        <th class="text-right">Total Revenue</th>
                        <th class="text-right">Avg per Booking</th>
                        <th class="text-center">Profit Margin</th>
                        <th class="text-center">Performance</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($services) && count($services) > 0)
                        @foreach($services as $serviceData)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="service-icon bg-primary rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-concierge-bell text-white"></i>
                                    </div>
                                    <div>
                                        <strong>{{ $serviceData['service']->name ?? 'N/A' }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            ${{ number_format($serviceData['service']->price ?? 0, 2) }}
                                            @if($serviceData['service']->duration)
                                                | {{ $serviceData['service']->duration }} min
                                            @endif
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-primary">{{ $serviceData['total_transactions'] }}</span>
                            </td>
                            <td class="text-right">
                                <strong>${{ number_format($serviceData['total_revenue'], 2) }}</strong>
                            </td>
                            <td class="text-right">
                                <span class="text-muted">${{ number_format($serviceData['average_transaction'], 2) }}</span>
                            </td>
                            <td class="text-center">
                                @php
                                    $margin = $serviceData['profit_margin'] ?? 0;
                                    $badgeClass = $margin >= 70 ? 'success' : ($margin >= 50 ? 'warning' : 'danger');
                                @endphp
                                <span class="badge badge-{{ $badgeClass }}">{{ number_format($margin, 1) }}%</span>
                            </td>
                            <td class="text-center">
                                @php
                                    $revenue = $serviceData['total_revenue'];
                                    $maxRevenue = $services->max('total_revenue');
                                    $performance = $maxRevenue > 0 ? ($revenue / $maxRevenue) * 100 : 0;
                                @endphp
                                <div class="progress" style="width: 80px;">
                                    <div class="progress-bar bg-{{ $performance >= 80 ? 'success' : ($performance >= 50 ? 'warning' : 'info') }}"
                                         style="width: {{ $performance }}%"></div>
                                </div>
                                <small class="text-muted">{{ number_format($performance, 0) }}%</small>
                            </td>
                        </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No service performance data available</p>
                            </td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Revenue by Service</h3>
            </div>
            <div class="card-body">
                <canvas id="serviceRevenueChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Top Performers</h3>
            </div>
            <div class="card-body">
                @if(isset($services) && count($services) > 0)
                    <div class="list-group list-group-flush">
                        @foreach($services->take(5) as $index => $serviceData)
                        <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                            <div>
                                <strong>{{ $serviceData['service']->name ?? 'N/A' }}</strong>
                                <br>
                                <small class="text-muted">{{ $serviceData['total_transactions'] }} bookings</small>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-{{ $index < 3 ? 'success' : 'secondary' }} badge-pill">
                                    #{{ $index + 1 }}
                                </span>
                                <br>
                                <small class="text-muted">${{ number_format($serviceData['total_revenue'], 0) }}</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-trophy fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No performance data</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-chart-line"></i><br>
                    Revenue Overview
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.analytics') }}" class="btn btn-outline-secondary btn-block">
                    <i class="fas fa-microscope"></i><br>
                    Advanced Analytics
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.customer-insights') }}" class="btn btn-outline-success btn-block">
                    <i class="fas fa-users"></i><br>
                    Customer Insights
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.export') }}" class="btn btn-outline-warning btn-block">
                    <i class="fas fa-download"></i><br>
                    Export Reports
                </a>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.opacity-75 {
    opacity: 0.75;
}

.service-icon {
    font-size: 14px;
}

.progress {
    height: 8px;
}

.list-group-item {
    padding: 0.75rem 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    initializeServiceRevenueChart();
});

function initializeServiceRevenueChart() {
    @if(isset($services) && count($services) > 0)
    var ctx = document.getElementById('serviceRevenueChart').getContext('2d');
    var services = @json($services->values());

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: services.map(item => item.service.name || 'N/A'),
            datasets: [{
                label: 'Revenue',
                data: services.map(item => item.total_revenue || 0),
                backgroundColor: '#007bff',
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Services'
                    }
                }
            }
        }
    });
    @endif
}
</script>
@stop
