@extends('owner.layouts.app')

@section('title', 'Advanced Revenue Analytics')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-microscope mr-2"></i>Advanced Analytics</h1>
            <p class="text-muted mb-0">Deep dive into customer behavior and revenue patterns</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <a href="{{ route('owner.revenue.export') }}" class="btn btn-primary">
                <i class="fas fa-download"></i> Export Data
            </a>
        </div>
    </div>
@stop

@section('content')
<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="card-title mb-0">
                    <i class="fas fa-filter mr-2"></i>
                    Advanced Analytics Dashboard
                </h3>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex justify-content-end">
                    <div class="input-group" style="width: 300px;">
                        <select name="period" class="form-control" onchange="this.form.submit()">
                            <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('period') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period') == 'month' || !request('period') ? 'selected' : '' }}>This Month</option>
                            <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>This Quarter</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Customer Lifetime Value -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-chart mr-2"></i>Customer Lifetime Value
                </h3>
            </div>
            <div class="card-body">
                @if(isset($customerLifetimeValue) && count($customerLifetimeValue) > 0)
                    <div class="row">
                        @foreach($customerLifetimeValue as $segment)
                        <div class="col-md-6 mb-3">
                            <div class="info-box bg-gradient-primary">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{{ $segment['segment'] ?? 'Segment' }}</span>
                                    <span class="info-box-number">${{ number_format($segment['avg_clv'] ?? 0, 2) }}</span>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ ($segment['avg_clv'] ?? 0) / 1000 * 100 }}%"></div>
                                    </div>
                                    <span class="progress-description">
                                        {{ $segment['customer_count'] ?? 0 }} customers
                                    </span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No customer lifetime value data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line mr-2"></i>Revenue Forecasting
                </h3>
            </div>
            <div class="card-body">
                @if(isset($revenueForecasting) && count($revenueForecasting) > 0)
                    <canvas id="forecastingChart" height="200"></canvas>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-crystal-ball fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No forecasting data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Cohort Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-layer-group mr-2"></i>Cohort Analysis
                </h3>
                <p class="card-text text-muted">Track customer retention over time</p>
            </div>
            <div class="card-body">
                @if(isset($cohortAnalysis) && count($cohortAnalysis) > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="thead-light">
                                <tr>
                                    <th>Cohort Month</th>
                                    <th>Customers</th>
                                    <th>Month 0</th>
                                    <th>Month 1</th>
                                    <th>Month 2</th>
                                    <th>Month 3</th>
                                    <th>Month 4</th>
                                    <th>Month 5</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($cohortAnalysis as $cohort)
                                <tr>
                                    <td class="font-weight-bold">{{ $cohort['period'] ?? 'N/A' }}</td>
                                    <td>{{ $cohort['total_customers'] ?? 0 }}</td>
                                    @for($i = 0; $i < 6; $i++)
                                        <td>
                                            @if(isset($cohort["month_$i"]))
                                                <span class="badge badge-{{ $cohort["month_$i"] >= 50 ? 'success' : ($cohort["month_$i"] >= 25 ? 'warning' : 'danger') }}">
                                                    {{ number_format($cohort["month_$i"], 1) }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    @endfor
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No cohort data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Seasonal Trends -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-week mr-2"></i>Seasonal Trends
                </h3>
                <p class="card-text text-muted">Identify patterns in revenue throughout the year</p>
            </div>
            <div class="card-body">
                @if(isset($seasonalTrends) && count($seasonalTrends) > 0)
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="seasonalTrendsChart" height="120"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h5>Key Insights</h5>
                            <ul class="list-unstyled">
                                @foreach($seasonalTrends as $trend)
                                <li class="mb-2">
                                    <strong>{{ $trend['period'] ?? 'N/A' }}</strong><br>
                                    <small class="text-muted">
                                        ${{ number_format($trend['avg_revenue'] ?? 0, 2) }} avg revenue
                                    </small>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-snowflake fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No seasonal trend data available</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Advanced Metrics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($customerLifetimeValue) && count($customerLifetimeValue) > 0)
                                ${{ number_format(collect($customerLifetimeValue)->avg('avg_clv'), 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Avg Customer LTV</p>
                    </div>
                    <i class="fas fa-user-plus fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($cohortAnalysis) && count($cohortAnalysis) > 0)
                                {{ number_format(collect($cohortAnalysis)->avg('month_1'), 1) }}%
                            @else
                                0%
                            @endif
                        </h3>
                        <p class="mb-0">Month 1 Retention</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($seasonalTrends) && count($seasonalTrends) > 0)
                                ${{ number_format(collect($seasonalTrends)->max('avg_revenue'), 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Peak Season Revenue</p>
                    </div>
                    <i class="fas fa-mountain fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">
                            @if(isset($revenueForecasting) && count($revenueForecasting) > 0)
                                ${{ number_format(collect($revenueForecasting)->sum('projected_revenue'), 2) }}
                            @else
                                $0.00
                            @endif
                        </h3>
                        <p class="mb-0">Projected Revenue</p>
                    </div>
                    <i class="fas fa-crystal-ball fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.index') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-chart-line"></i><br>
                    Revenue Overview
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.service-performance') }}" class="btn btn-outline-info btn-block">
                    <i class="fas fa-chart-bar"></i><br>
                    Service Performance
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.customer-insights') }}" class="btn btn-outline-success btn-block">
                    <i class="fas fa-users"></i><br>
                    Customer Insights
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.export') }}" class="btn btn-outline-warning btn-block">
                    <i class="fas fa-download"></i><br>
                    Export Reports
                </a>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.opacity-75 {
    opacity: 0.75;
}

.info-box {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.info-box-content {
    color: white !important;
}

.info-box-icon {
    border-radius: 0.5rem 0 0 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

.chart-container {
    position: relative;
    height: 300px;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize charts
    initializeForecastingChart();
    initializeSeasonalTrendsChart();
});

function initializeForecastingChart() {
    @if(isset($revenueForecasting) && count($revenueForecasting) > 0)
    var ctx = document.getElementById('forecastingChart').getContext('2d');
    var forecastData = @json($revenueForecasting);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: forecastData.map(item => item.period || 'N/A'),
            datasets: [{
                label: 'Projected Revenue',
                data: forecastData.map(item => item.projected_revenue || 0),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                }
            }
        }
    });
    @endif
}

function initializeSeasonalTrendsChart() {
    @if(isset($seasonalTrends) && count($seasonalTrends) > 0)
    var ctx = document.getElementById('seasonalTrendsChart').getContext('2d');
    var seasonalData = @json($seasonalTrends);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: seasonalData.map(item => item.period || 'N/A'),
            datasets: [{
                label: 'Average Revenue',
                data: seasonalData.map(item => item.avg_revenue || 0),
                backgroundColor: '#28a745',
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                }
            }
        }
    });
    @endif
}
</script>
@stop
