@extends('admin.layouts.app')

@section('title', 'Advanced Roles & Permissions Management')

@section('header')
    <h1>
        <i class="fas fa-shield-alt"></i> Advanced Roles & Permissions Management
        <small class="text-muted">Hierarchical Security System</small>
    </h1>
@stop

@section('main-content')
<!-- Security Statistics Dashboard -->
<div class="row mb-4">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $statistics['total_roles'] ?? 0 }}</h3>
                <p>Total Roles</p>
            </div>
            <div class="icon">
                <i class="fas fa-users-cog"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $statistics['system_roles'] ?? 0 }}</h3>
                <p>System Roles</p>
            </div>
            <div class="icon">
                <i class="fas fa-lock"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $statistics['high_security_roles'] ?? 0 }}</h3>
                <p>High Security Roles</p>
            </div>
            <div class="icon">
                <i class="fas fa-shield-alt"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $statistics['high_risk_activities'] ?? 0 }}</h3>
                <p>High Risk Activities (24h)</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-filter"></i> Advanced Filters & Search
        </h3>
        <div class="card-tools">
            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.roles.index') }}" class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="search">Search Roles</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Role name or description...">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="hierarchy_level">Hierarchy Level</label>
                    <select class="form-control" id="hierarchy_level" name="hierarchy_level">
                        <option value="">All Levels</option>
                        @if(class_exists('\App\Models\EnhancedRole'))
                            @foreach(\App\Models\EnhancedRole::HIERARCHY_LEVELS as $level => $name)
                                <option value="{{ $level }}" {{ request('hierarchy_level') == $level ? 'selected' : '' }}>
                                    {{ $level }} - {{ $name }}
                                </option>
                            @endforeach
                        @else
                            <option value="0">Super Admin</option>
                            <option value="1">Admin</option>
                            <option value="2">Manager</option>
                            <option value="3">Staff</option>
                            <option value="4">Customer</option>
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="security_level">Security Level</label>
                    <select class="form-control" id="security_level" name="security_level">
                        <option value="">All Levels</option>
                        @if(class_exists('\App\Models\EnhancedRole'))
                            @foreach(\App\Models\EnhancedRole::SECURITY_LEVELS as $level => $name)
                                <option value="{{ $level }}" {{ request('security_level') == $level ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        @else
                            <option value="1">Low</option>
                            <option value="2">Medium</option>
                            <option value="3">High</option>
                            <option value="4">Critical</option>
                            <option value="5">Maximum</option>
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="is_system_role">Role Type</label>
                    <select class="form-control" id="is_system_role" name="is_system_role">
                        <option value="">All Types</option>
                        <option value="1" {{ request('is_system_role') === '1' ? 'selected' : '' }}>System Roles</option>
                        <option value="0" {{ request('is_system_role') === '0' ? 'selected' : '' }}>Custom Roles</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Main Roles Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-table"></i> Roles Management
        </h3>
        <div class="card-tools">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-info btn-sm" id="2faToggleBtn" title="Toggle 2FA for Role Operations">
                    <i class="fas fa-shield-alt"></i>
                    <span id="2faStatus">2FA</span>
                </button>
                <a href="{{ route('admin.settings.two-factor') }}" class="btn btn-outline-secondary btn-sm" title="2FA Settings">
                    <i class="fas fa-cog"></i>
                </a>
                @can('create roles')
                <a href="{{ route('admin.roles.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Create New Role
                </a>
                @endcan
                <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#roleHelpModal">
                    <i class="fas fa-question-circle"></i> Help
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover" id="roles-table">
                <thead class="thead-dark">
                    <tr>
                        <th width="5%">ID</th>
                        <th width="15%">Role Name</th>
                        <th width="10%">Hierarchy</th>
                        <th width="10%">Security</th>
                        <th width="10%">Type</th>
                        <th width="20%">Permissions</th>
                        <th width="8%">Users</th>
                        <th width="10%">Created</th>
                        <th width="12%">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($roles as $role)
                    <tr class="{{ (isset($role->is_system_role) && $role->is_system_role) ? 'table-warning' : '' }}">
                        <td>
                            <span class="badge badge-secondary">{{ $role->id }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                @if(isset($role->is_system_role) && $role->is_system_role)
                                    <i class="fas fa-lock text-warning mr-2" title="System Role"></i>
                                @endif
                                <div>
                                    <strong>{{ $role->name }}</strong>
                                    @if($role->description)
                                        <br><small class="text-muted">{{ Str::limit($role->description, 50) }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            @php
                                $hierarchyLevel = $role->hierarchy_level ?? 5;
                            @endphp
                            <span class="badge badge-{{ $hierarchyLevel <= 1 ? 'danger' : ($hierarchyLevel <= 3 ? 'warning' : 'info') }}">
                                Level {{ $hierarchyLevel }}
                            </span>
                            <br><small class="text-muted">
                                @if(class_exists('\App\Models\EnhancedRole'))
                                    {{ \App\Models\EnhancedRole::HIERARCHY_LEVELS[$hierarchyLevel] ?? 'Unknown' }}
                                @else
                                    @php
                                        $hierarchyNames = [0 => 'Super Admin', 1 => 'Admin', 2 => 'Business Owner', 3 => 'Manager', 4 => 'Staff', 5 => 'Customer'];
                                    @endphp
                                    {{ $hierarchyNames[$hierarchyLevel] ?? 'Unknown' }}
                                @endif
                            </small>
                        </td>
                        <td>
                            @php
                                $securityLevel = $role->security_level ?? 2;
                                $securityColors = [1 => 'success', 2 => 'info', 3 => 'warning', 4 => 'danger', 5 => 'dark'];
                                $securityColor = $securityColors[$securityLevel] ?? 'secondary';
                            @endphp
                            <span class="badge badge-{{ $securityColor }}">
                                @if(class_exists('\App\Models\EnhancedRole'))
                                    {{ \App\Models\EnhancedRole::SECURITY_LEVELS[$securityLevel] ?? 'Unknown' }}
                                @else
                                    @php
                                        $securityNames = [1 => 'Low', 2 => 'Medium', 3 => 'High', 4 => 'Critical', 5 => 'Maximum'];
                                    @endphp
                                    {{ $securityNames[$securityLevel] ?? 'Unknown' }}
                                @endif
                            </span>
                        </td>
                        <td>
                            @if(isset($role->is_system_role) && $role->is_system_role)
                                <span class="badge badge-warning">
                                    <i class="fas fa-shield-alt"></i> System
                                </span>
                            @else
                                <span class="badge badge-primary">
                                    <i class="fas fa-user-cog"></i> Custom
                                </span>
                            @endif
                        </td>
                        <td>
                            <div class="permission-badges">
                                @if($role->permissions->count() > 0)
                                    @foreach($role->permissions->take(3) as $permission)
                                        <span class="badge badge-info badge-sm mb-1">{{ $permission->name }}</span>
                                    @endforeach
                                    @if($role->permissions->count() > 3)
                                        <span class="badge badge-secondary badge-sm mb-1">
                                            +{{ $role->permissions->count() - 3 }} more
                                        </span>
                                    @endif
                                @else
                                    <span class="text-muted">No permissions</span>
                                @endif
                            </div>
                        </td>
                        <td class="text-center">
                            @if($role->users->count() > 0)
                                <span class="badge badge-success badge-lg">{{ $role->users->count() }}</span>
                            @else
                                <span class="badge badge-light">0</span>
                            @endif
                            @if($role->max_users)
                                <br><small class="text-muted">Max: {{ $role->max_users }}</small>
                            @endif
                        </td>
                        <td>
                            <small>
                                {{ $role->created_at->format('M d, Y') }}
                                <br>{{ $role->created_at->format('H:i') }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm" role="group">
                                <a href="{{ route('admin.roles.show', $role) }}"
                                   class="btn btn-info btn-xs" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>

                                @can('edit roles')
                                    @if(auth()->user()->hasRole('Super Admin') || !(isset($role->is_system_role) && $role->is_system_role))
                                        <a href="{{ route('admin.roles.edit', $role) }}"
                                           class="btn btn-warning btn-xs" title="Edit Role">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    @else
                                        <button type="button" class="btn btn-warning btn-xs" disabled
                                                title="Cannot edit system roles">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    @endif
                                @endcan

                                @can('delete roles')
                                    @if(!(isset($role->is_system_role) && $role->is_system_role) && $role->users->count() == 0)
                                        <button type="button" class="btn btn-danger btn-xs"
                                                onclick="confirmDelete('{{ $role->id }}', '{{ $role->name }}')"
                                                title="Delete Role">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @else
                                        <button type="button" class="btn btn-danger btn-xs" disabled
                                                title="{{ (isset($role->is_system_role) && $role->is_system_role) ? 'Cannot delete system roles' : 'Cannot delete role with assigned users' }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-users-slash fa-3x mb-3"></i>
                            <br>No roles found matching your criteria.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                Showing {{ $roles->firstItem() ?? 0 }} to {{ $roles->lastItem() ?? 0 }} of {{ $roles->total() }} roles
            </div>
            <div>
                {{ $roles->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="roleHelpModal" tabindex="-1" role="dialog" aria-labelledby="roleHelpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title" id="roleHelpModalLabel">
                    <i class="fas fa-question-circle"></i> Role & Permission Management Help
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-layer-group text-primary"></i> Hierarchy Levels</h6>
                        <ul class="list-unstyled">
                            @if(class_exists('\App\Models\EnhancedRole'))
                                @foreach(\App\Models\EnhancedRole::HIERARCHY_LEVELS as $level => $name)
                                    <li class="mb-2">
                                        <span class="badge badge-{{ $level <= 1 ? 'danger' : ($level <= 3 ? 'warning' : 'info') }}">
                                            Level {{ $level }}
                                        </span>
                                        <strong>{{ $name }}</strong>
                                        <br><small class="text-muted">
                                            @switch($level)
                                                @case(0) Highest privilege - System administration @break
                                                @case(1) Administrative operations @break
                                                @case(2) Business-specific management @break
                                                @case(3) Operational management @break
                                                @case(4) Basic operations @break
                                                @case(5) End user access @break
                                            @endswitch
                                        </small>
                                    </li>
                                @endforeach
                            @else
                                <li class="mb-2">
                                    <span class="badge badge-danger">Level 0</span>
                                    <strong>Super Admin</strong>
                                    <br><small class="text-muted">Highest privilege - System administration</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-warning">Level 1</span>
                                    <strong>Admin</strong>
                                    <br><small class="text-muted">Administrative operations</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-warning">Level 2</span>
                                    <strong>Manager</strong>
                                    <br><small class="text-muted">Operational management</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-info">Level 3</span>
                                    <strong>Staff</strong>
                                    <br><small class="text-muted">Basic operations</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-info">Level 4</span>
                                    <strong>Customer</strong>
                                    <br><small class="text-muted">End user access</small>
                                </li>
                            @endif
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-warning"></i> Security Levels</h6>
                        <ul class="list-unstyled">
                            @if(class_exists('\App\Models\EnhancedRole'))
                                @foreach(\App\Models\EnhancedRole::SECURITY_LEVELS as $level => $name)
                                    @php
                                        $colors = [1 => 'success', 2 => 'info', 3 => 'warning', 4 => 'danger', 5 => 'dark'];
                                    @endphp
                                    <li class="mb-2">
                                        <span class="badge badge-{{ $colors[$level] }}">{{ $name }}</span>
                                        <br><small class="text-muted">
                                            @switch($level)
                                                @case(1) Basic security requirements @break
                                                @case(2) Standard security measures @break
                                                @case(3) Enhanced security protocols @break
                                                @case(4) High security with audit trails @break
                                                @case(5) Maximum security with encryption @break
                                            @endswitch
                                        </small>
                                    </li>
                                @endforeach
                            @else
                                <li class="mb-2">
                                    <span class="badge badge-success">Low</span>
                                    <br><small class="text-muted">Basic security requirements</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-info">Medium</span>
                                    <br><small class="text-muted">Standard security measures</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-warning">High</span>
                                    <br><small class="text-muted">Enhanced security protocols</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-danger">Critical</span>
                                    <br><small class="text-muted">High security with audit trails</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge badge-dark">Maximum</span>
                                    <br><small class="text-muted">Maximum security with encryption</small>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>

                <hr>

                <h6><i class="fas fa-info-circle text-info"></i> Security Features</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Hierarchical role inheritance</li>
                            <li><i class="fas fa-check text-success"></i> Permission-based access control</li>
                            <li><i class="fas fa-check text-success"></i> Audit logging for all changes</li>
                            <li><i class="fas fa-check text-success"></i> Privilege escalation protection</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> System role protection</li>
                            <li><i class="fas fa-check text-success"></i> Real-time security monitoring</li>
                            <li><i class="fas fa-check text-success"></i> Encrypted sensitive permissions</li>
                            <li><i class="fas fa-check text-success"></i> Role template system</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white" id="deleteConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Role Deletion
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the role <strong id="roleNameToDelete"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The role will be permanently removed from the system.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteRoleForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Role
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable with advanced features
    $('#roles-table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "paging": false,
        "info": false,
        "searching": false, // We use custom search
        "ordering": true,
        "order": [[ 2, "asc" ]], // Order by hierarchy level
        "columnDefs": [
            { "orderable": false, "targets": [5, 8] }, // Disable ordering for permissions and actions
            { "searchable": false, "targets": [8] } // Disable search for actions
        ]
    });

    // Load 2FA status
    load2FAStatus();

    // 2FA Toggle functionality
    $('#2faToggleBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true);

        // Get current status and determine action
        $.get('{{ route("admin.settings.two-factor.status") }}', function(response) {
            const action = response.settings.required_for_roles ? 'disable_roles' : 'enable_roles';

            $.ajax({
                url: '{{ route("admin.settings.two-factor.toggle") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    action: action
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        load2FAStatus(); // Refresh status
                    } else {
                        toastr.error(response.message);
                    }
                    btn.prop('disabled', false);
                },
                error: function() {
                    toastr.error('Failed to toggle 2FA settings.');
                    btn.prop('disabled', false);
                }
            });
        });
    });

    // Auto-refresh statistics every 30 seconds
    setInterval(function() {
        load2FAStatus();
    }, 30000);

    // Tooltip initialization with error handling
    try {
        if (typeof $.fn.tooltip !== 'undefined') {
            $('[data-toggle="tooltip"]').tooltip();
            console.log('Tooltips initialized in admin roles');
        } else {
            console.warn('Bootstrap tooltip plugin not available in admin roles');
        }
    } catch (error) {
        console.error('Error initializing tooltips in admin roles:', error);
    }
});

// Load 2FA status
function load2FAStatus() {
    $.get('{{ route("admin.settings.two-factor.status") }}', function(response) {
        const settings = response.settings;
        const btn = $('#2faToggleBtn');
        const status = $('#2faStatus');

        if (settings.enabled && settings.required_for_roles) {
            btn.removeClass('btn-outline-info btn-outline-secondary')
               .addClass('btn-success');
            status.text('2FA ON');
            btn.attr('title', '2FA is enabled for role operations - Click to disable');
        } else if (settings.enabled) {
            btn.removeClass('btn-success btn-outline-secondary')
               .addClass('btn-outline-info');
            status.text('2FA OFF');
            btn.attr('title', '2FA is disabled for role operations - Click to enable');
        } else {
            btn.removeClass('btn-success btn-outline-info')
               .addClass('btn-outline-secondary');
            status.text('2FA DISABLED');
            btn.attr('title', '2FA is globally disabled - Go to settings to enable');
        }
    }).fail(function() {
        // Fallback if status check fails
        $('#2faStatus').text('2FA');
    });
}

// Delete confirmation function
function confirmDelete(roleId, roleName) {
    $('#roleNameToDelete').text(roleName);
    $('#deleteRoleForm').attr('action', '{{ route("admin.roles.index") }}/' + roleId);
    $('#deleteConfirmModal').modal('show');
}

// Real-time search functionality
$('#search').on('keyup', function() {
    var table = $('#roles-table').DataTable();
    table.search(this.value).draw();
});

// Security level color coding
function getSecurityLevelColor(level) {
    const colors = {
        1: 'success',
        2: 'info',
        3: 'warning',
        4: 'danger',
        5: 'dark'
    };
    return colors[level] || 'secondary';
}

// Hierarchy level color coding
function getHierarchyLevelColor(level) {
    if (level <= 1) return 'danger';
    if (level <= 3) return 'warning';
    return 'info';
}
</script>

@if(session('success'))
<script>
$(document).ready(function() {
    toastr.success('{{ session('success') }}');
});
</script>
@endif

@if(session('error'))
<script>
$(document).ready(function() {
    toastr.error('{{ session('error') }}');
});
</script>
@endif
@stop
